import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import {
  AssessmentTools,
  BusinessContinuityScreens,
  RouteKey,
  UserType,
} from 'types/enum';
import {
  screenToQuestion,
  screenToQuestionSecondary,
  screenToQuestionTertiary,
} from 'views/BusinessContinuity/Report/continuityReportHelper';
import ContinuityReportRowData from 'views/BusinessContinuity/Report/ContinuityReportRowData';
import MessageWishesReport from 'views/BusinessContinuity/Report/MessageWishesReport';
import MiscellaneousReportData from 'views/BusinessContinuity/Report/MiscellaneousReportData';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { filterEmptyArrayObjects } from 'utils/helpers/Helpers';
import { renderToString } from 'react-dom/server';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import Spinner from 'shared-resources/components/Spinner/Spinner';

interface ContinuityBuilderDataProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments: any[] };
  }) => void;
}

const ContinuityBuilderData: React.FC<ContinuityBuilderDataProps> = ({
  onSelectionChange,
}) => {
  const dispatch = useDispatch();
  const { id } = useParams<{ id: string }>();
  const [selectedScreens, setSelectedScreens] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});

  const loggedInUserData = useSelector(getUserData);
  const response: any = useSelector(getAssessmentReportResponse);
  const loading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);
  const filteredResponse = filterEmptyArrayObjects(
    response?.assessment_response
  );
  const navigate = useNavigate();

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(fetchComments(Number(id), AssessmentTools.BUSINESS_CONTINUITY));
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    [dispatch]
  );

  const handleCheckboxChange = useCallback((screen: string) => {
    setSelectedScreens((prev) => ({
      ...prev,
      [screen]: !prev[screen],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const renderScreenDataForQuill = useCallback(
    (screen: string) => {
      if (
        screen === BusinessContinuityScreens.BOARD_CHAIRMAN &&
        filteredResponse?.board_chairman &&
        Object.keys(filteredResponse?.board_chairman)?.length
      ) {
        const chairmanData = filteredResponse?.board_chairman;
        return (
          <div
            key={screen}
            className='mt-2 flex flex-col gap-4 w-full relative'
          >
            {chairmanData?.is_chairman !== undefined && (
              <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
                {chairmanData.is_chairman ? 'Yes' : 'No'}
              </div>
            )}
            {chairmanData?.name_of_director && (
              <div className='flex flex-col gap-4'>
                <span className='font-semibold'>
                  Who is the Chairman of the Board?
                </span>
                <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
                  {chairmanData.name_of_director}
                </div>
              </div>
            )}
          </div>
        );
      }

      if (
        screen === BusinessContinuityScreens.SOLE_OWNERS &&
        filteredResponse?.sole_owners &&
        Object.keys(filteredResponse?.sole_owners)?.length
      ) {
        const soleOwnerData = filteredResponse?.sole_owners;
        return (
          <div
            key={screen}
            className='mt-2 flex flex-col gap-4 w-full relative'
          >
            <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
              {soleOwnerData?.is_sole_owner ? 'Sole Owner' : 'Partners'}
            </div>
            {!!soleOwnerData?.partners?.length && (
              <ContinuityReportRowData
                fieldKey='partners'
                responseData={soleOwnerData}
                rowClassname='max-w-full !left-0'
              />
            )}
          </div>
        );
      }

      // Default case for other screens
      return (
        filteredResponse[screen] && (
          <ContinuityReportRowData
            fieldKey={screen}
            responseData={filteredResponse}
            rowClassname='max-w-full !left-0'
          />
        )
      );
    },
    [filteredResponse]
  );

  const selectedData = useMemo(() => {
    const data: {
      [key: string]: { content: string; type: string; comments: any[] };
    } = {};

    Object.entries({
      ...screenToQuestion,
      ...screenToQuestionSecondary,
      ...screenToQuestionTertiary,
    }).forEach(([screen, question]) => {
      if (selectedScreens[screen]) {
        const content = renderToString(
          <div className='relative'>
            <span className='text-lg font-semibold mb-2'>{question}</span>
            {renderScreenDataForQuill(screen)}
          </div>
        );

        const screenComments = comments.filter(
          (comment) =>
            comment.metadata?.screen === screen &&
            selectedComments[comment.id.toString()]
        );

        data[screen] = {
          content,
          type: 'continuity-data',
          comments: screenComments,
        };
      }
    });

    return data;
  }, [selectedScreens, selectedComments, renderScreenDataForQuill, comments]);

  useEffect(() => {
    onSelectionChange(selectedData);
  }, [selectedData, onSelectionChange]);

  const renderScreenData = (screen: string, question: string) =>
    filteredResponse[screen] && (
      <div key={screen} className='mt-4 flex flex-col gap-4 w-full relative'>
        <Checkbox
          onChange={() => handleCheckboxChange(screen)}
          value={selectedScreens[screen] || false}
          text={question as any}
          className='!mt-0'
        />
        {renderScreenDataForQuill(screen)}
        <div className='mt-2'>
          {comments.filter((comment) => comment.metadata?.screen === screen)
            .length > 0 ? (
            <span className='font-semibold mb-2'>Comments:</span>
          ) : null}
          {comments
            .filter((comment) => comment.metadata?.screen === screen)
            .map((comment) => (
              <div key={comment.id} className='ml-4 mb-2 flex items-center'>
                <Checkbox
                  onChange={() =>
                    handleCommentCheckboxChange(comment.id.toString())
                  }
                  value={selectedComments[comment.id.toString()] || false}
                  text={comment.comment as any}
                  className='!mt-0'
                />
              </div>
            ))}
        </div>
      </div>
    );

  const showMiscellaneous =
    (loggedInUserData?.type === UserType.ADVISOR &&
      !filteredResponse?.should_private?.is_private) ||
    loggedInUserData?.type === UserType.BUSINESS_OWNER;

  if (loading) {
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-18rem)] overflow-y-auto scrollbar pr-4'>
      {Object.entries(screenToQuestion).map(([screen, question]) =>
        renderScreenData(screen, question)
      )}

      {Object.entries(screenToQuestionSecondary).map(([screen, question]) =>
        renderScreenData(screen, question)
      )}

      {Object.entries(screenToQuestionTertiary).map(([screen, question]) => {
        if (
          screen === BusinessContinuityScreens.MISCELLANEOUS &&
          filteredResponse?.miscellaneous &&
          showMiscellaneous
        ) {
          return (
            <div key={screen}>
              <Checkbox
                onChange={() => handleCheckboxChange(screen)}
                value={selectedScreens[screen] || false}
                text={question as any}
                className='!mt-0'
              />
              <MiscellaneousReportData data={filteredResponse.miscellaneous} />
              <div className='mt-2'>
                {comments.filter(
                  (comment) => comment.metadata?.screen === screen
                )?.length > 0 ? (
                  <span className='font-semibold mb-2'>Comments:</span>
                ) : null}
                {comments
                  .filter((comment) => comment.metadata?.screen === screen)
                  .map((comment) => (
                    <div
                      key={comment.id}
                      className='ml-4 mb-2 flex items-center'
                    >
                      <Checkbox
                        onChange={() =>
                          handleCommentCheckboxChange(comment.id.toString())
                        }
                        value={selectedComments[comment.id.toString()] || false}
                        text={comment.comment as any}
                        className='!mt-0'
                      />
                    </div>
                  ))}
              </div>
            </div>
          );
        }
        if (
          screen === BusinessContinuityScreens.MESSAGE_WISHES &&
          filteredResponse?.message_wishes
        ) {
          return (
            <div key={screen}>
              <Checkbox
                onChange={() => handleCheckboxChange(screen)}
                value={selectedScreens[screen] || false}
                text={question as any}
                className='!mt-0'
              />
              <MessageWishesReport data={filteredResponse.message_wishes} />
              <div className='mt-2'>
                {comments.filter(
                  (comment) => comment.metadata?.screen === screen
                )?.length > 0 ? (
                  <span className='font-semibold mb-2'>Comments:</span>
                ) : null}
                {comments
                  .filter((comment) => comment.metadata?.screen === screen)
                  .map((comment) => (
                    <div
                      key={comment.id}
                      className='ml-4 mb-2 flex items-center'
                    >
                      <Checkbox
                        onChange={() =>
                          handleCommentCheckboxChange(comment.id.toString())
                        }
                        value={selectedComments[comment.id.toString()] || false}
                        text={comment.comment as any}
                        className='!mt-0'
                      />
                    </div>
                  ))}
              </div>
            </div>
          );
        }
        return null;
      })}
    </div>
  );
};

export default ContinuityBuilderData;
