import React, {
  useState,
  useRef,
  useCallback,
  useMemo,
  useEffect,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import Editor from 'shared-resources/components/QuillEditor/Editor';
import { AssessmentTools, UserType } from 'types/enum';
import Button from 'shared-resources/components/Button/Button';
import {
  getCurrentReport,
  getIsReportCreated,
  getReportBuilderLoading,
  getReports,
} from 'store/selectors/report-builder.selector';
import { getUserData } from 'store/selectors/user.selector';
import { useParams } from 'react-router';
import {
  createReport,
  fetchReport,
  fetchReports,
  sendReportToBO,
} from 'store/actions/report-builder.action';
import { FaDownload, FaPaperPlane } from 'react-icons/fa';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import { renderToString } from 'react-dom/server';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getKey, getToolName } from 'utils/helpers/Helpers';
import {
  setCurrentReport,
  setIsReportCreated,
  setReports,
} from 'store/reducers/report-builder.reducer';
import ToolSidebar from './ToolSidebar';
import ToolDataPopup from './ToolDataPopup';
import ContinuityBuilderData from './ContinuityBuilderData';
import ReadinessBuilder from './ReadinessBuilder';
import OwnerRelianceBuilder from './OwnerRelianceBuilder';
import TransitionBuilder from './TransitionBuilder';
import WealthBuilder from './WealthBuilder';
import AssetProtectionBuilder from './AssetProtectionBuilder';
import ValueEnhancementBuilder from './ValueEnhancementBuilder';
import StakeholderAlignmentBuilder from './StakeholderAlignmentBuilder';
import CreateReportModal from './CreateReportModal';
import BuyerTypeBuilder from './BuyerTypeBuilder';

const ReportBuilder: React.FC = () => {
  const editorRef = useRef<Quill | null>(null);
  const [selectedTool, setSelectedTool] = useState<AssessmentTools | null>(
    null
  );
  const [selectedData, setSelectedData] = useState<{
    [key: string]: { content: string; type: string; comments?: any[] };
  }>({});
  const dispatch = useDispatch();
  const reportLoading = useSelector(getReportBuilderLoading);
  const loggedInUserData = useSelector(getUserData);
  const reports = useSelector(getReports);
  const currentReport = useSelector(getCurrentReport);
  const { id } = useParams();
  const [editorContent, setEditorContent] = useState<string>('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const isReportCreated = useSelector(getIsReportCreated);
  const isBusinessOwner = loggedInUserData?.type === UserType.BUSINESS_OWNER;

  // Add new state for PDF view
  const [pdfPages, setPdfPages] = useState<HTMLElement[]>([]);

  useEffect(() => {
    if (id && loggedInUserData) dispatch(fetchReports(+id));
  }, [dispatch, id, loggedInUserData]);

  useEffect(() => {
    if (reports.length && loggedInUserData) {
      if ((loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN) && id) {
        dispatch(fetchReport(reports[reports.length - 1]?.id, +id));
      } else {
        dispatch(
          fetchReport(
            reports[reports.length - 1]?.id,
            loggedInUserData.id as number
          )
        );
      }
    }
  }, [dispatch, reports]);

  useEffect(() => {
    if (currentReport && currentReport.file && editorRef.current) {
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        const contents = e.target?.result as string;
        if (editorRef.current) {
          editorRef.current.root.innerHTML = contents;
        }
      };
      reader.readAsText(currentReport.file);
    }
  }, [currentReport]);

  const handleToolSelect = useCallback((tool: AssessmentTools) => {
    setSelectedTool(tool);
  }, []);

  // finds the tool section
  const findToolSection = useCallback(
    (editor: Quill, toolName: AssessmentTools) => {
      const delta = editor.getContents();
      let toolSectionIndex = -1;
      let endIndex = -1;
      let currentIndex = 0;

      delta.ops?.forEach((op) => {
        if (
          op.insert &&
          typeof op.insert === 'object' &&
          op.insert['tool-section'] &&
          (op.insert['tool-section'] as any).toolName === getToolName(toolName)
        ) {
          toolSectionIndex = currentIndex;
        } else if (
          toolSectionIndex !== -1 &&
          endIndex === -1 &&
          op.insert &&
          typeof op.insert === 'object' &&
          op.insert['tool-section']
        ) {
          endIndex = currentIndex;
        }

        if (typeof op.insert === 'string') {
          currentIndex += op.insert.length;
        } else {
          currentIndex += 1;
        }
      });

      return {
        start: toolSectionIndex,
        end: endIndex === -1 ? currentIndex : endIndex,
      };
    },
    []
  );

  // Finds the start and end positions of a tool's section in the editor
  // The start position is the index of the first tool section,
  // and the end position is the index of the last tool section,
  // save the selected data at end of that section
  const handleSave = useCallback(() => {
    const editor = editorRef.current;
    if (editor && Object.keys(selectedData).length > 0 && selectedTool) {
      const { start, end } = findToolSection(editor, selectedTool);

      if (start === -1) {
        const originalRange = editor.getSelection();
        let insertPosition = editor.getLength();

        editor.insertEmbed(insertPosition, 'tool-section', {
          toolName: getToolName(selectedTool),
        });
        editor.insertText(insertPosition + 1, '\n\n');
        insertPosition += 2;

        // Process each data entry in order
        Object.values(selectedData).forEach((data) => {
          // Insert react component if it exists
          if (data.content) {
            editor.insertEmbed(insertPosition, 'react-component', {
              content: data.content,
              type: data.type,
            });
            insertPosition += 1;
            editor.insertText(insertPosition, '\n');
            insertPosition += 1;
          }

          // Insert comments if they exist
          if (data.comments && data.comments.length > 0) {
            editor.insertText(insertPosition, 'Comments:\n');
            insertPosition += 10;

            data.comments.forEach((comment) => {
              editor.insertText(insertPosition, `${comment.comment}\n`, {
                'comment-id': comment.id,
                'data-screen': comment.metadata?.screen,
                class: 'comment-block',
              });
              insertPosition += comment.comment.length + 1; // +1 for newline
            });

            editor.insertText(insertPosition, '\n');
            insertPosition += 1;
          }
        });

        if (originalRange) {
          editor.setSelection(originalRange);
        }
      } else {
        const originalRange = editor.getSelection();
        let insertPosition = end;

        // Process each data entry in order
        Object.values(selectedData).forEach((data) => {
          // Insert react component if it exists
          if (data.content) {
            editor.insertEmbed(insertPosition, 'react-component', {
              content: data.content,
              type: data.type,
            });
            insertPosition += 1;
            editor.insertText(insertPosition, '\n');
            insertPosition += 1;
          }

          // Insert comments if they exist
          if (data.comments && data.comments.length > 0) {
            editor.insertText(insertPosition, 'Comments:\n\n');
            insertPosition += 10;

            data.comments.forEach((comment) => {
              editor.insertText(insertPosition, `${comment.comment}\n`, {
                'comment-id': comment.id,
                'data-screen': comment.metadata?.screen,
                class: 'comment-block',
              });
              insertPosition += comment.comment.length + 1; // +1 for newline
            });

            editor.insertText(insertPosition, '\n');
            insertPosition += 1;
          }
        });

        if (originalRange) {
          editor.setSelection(originalRange);
        }
      }
    }

    setSelectedTool(null);
    setSelectedData({});
  }, [selectedData, selectedTool, findToolSection]);

  const handleClose = useCallback(() => {
    setSelectedTool(null);
    setSelectedData({});
  }, []);

  const handleSelectionChange = useCallback(
    (data: {
      [key: string]: { content: string; type: string; comments?: any[] };
    }) => {
      setSelectedData(data);
    },
    []
  );

  const divideContentIntoPages = (
    content: string,
    pageHeight: number
  ): string[] => {
    const tempDiv = document.createElement('div');
    tempDiv.className = 'ql-editor-pdf';
    tempDiv.innerHTML = content;
    const pages: string[] = [];
    let currentPage = document.createElement('div');
    currentPage.className = 'ql-editor-pdf';
    let currentHeight = 0;

    // Create a temporary container
    const measureContainer = document.createElement('div');
    measureContainer.style.position = 'absolute';
    measureContainer.style.visibility = 'hidden';
    measureContainer.style.width = '210mm'; // A4 width
    measureContainer.className = 'ql-editor-pdf';
    document.body.appendChild(measureContainer);

    tempDiv.childNodes.forEach((node) => {
      const element = node.cloneNode(true) as HTMLElement;

      // Append the element to the measure container
      measureContainer.appendChild(element);

      // Measure the height
      const elementHeight = element.offsetHeight;

      if (
        currentHeight + elementHeight > pageHeight &&
        currentPage.childNodes.length > 0
      ) {
        pages.push(currentPage.innerHTML);
        currentPage = document.createElement('div');
        currentPage.className = 'ql-editor-pdf';
        currentHeight = 0;
      }

      currentPage.appendChild(element.cloneNode(true));
      currentHeight += elementHeight;

      // Remove the element from the measure container
      measureContainer.removeChild(element);
    });

    if (currentPage.childNodes.length > 0) {
      pages.push(currentPage.innerHTML);
    }

    // Remove the temporary container
    document.body.removeChild(measureContainer);

    return pages.filter((page) => page.trim() !== ''); // Filter out empty pages
  };

  const handleEditorChange = useCallback((content: string) => {
    setEditorContent(content);
  }, []);

  useEffect(() => {
    if (editorContent && !reportLoading) {
      const pages = divideContentIntoPages(editorContent, 1500);
      const renderedPages = pages.map((pageContent, index) => {
        const reportContent = document.createElement('div');
        reportContent.style.position = 'absolute';
        reportContent.style.left = '-9999px';
        reportContent.innerHTML = renderToString(
          <LightThemeReportWrapper id={`report-page-${index}`}>
            <div className='p-12 ql-editor-pdf'>
              {React.createElement('div', {
                dangerouslySetInnerHTML: { __html: pageContent },
              })}
            </div>
          </LightThemeReportWrapper>
        );
        document.body.appendChild(reportContent);
        return reportContent;
      });
      setPdfPages(renderedPages);
    }
  }, [editorContent, reportLoading]);

  const handleCreateReport = useCallback(
    (reportTitle: string) => {
      if (editorRef.current) {
        const content = editorRef.current.root.innerHTML;
        const fileName = `report_${Date.now()}.txt`;

        dispatch(
          createReport({
            businessOwnerId: +id!,
            fileName,
            content,
            reportTitle,
          })
        );
        setIsCreateModalOpen(false);
      }
    },
    [dispatch, id]
  );

  const handleDownload = async () => {
    if (editorRef.current) {
      const pages = pdfPages.map(
        (page, index) =>
          page.querySelector(`#report-page-${index}`) as HTMLElement
      );
      await generatePdf(
        pages,
        'CustomReport.pdf',
        () => {
          // Clean up pre-rendered pages
          pdfPages.forEach((page) => document.body.removeChild(page));
        },
        'portrait'
      );
    }
  };

  const isEditorEmpty =
    editorContent?.trim().length === 0 || editorContent === '<p><br></p>';

  // Memoized components (unchanged)
  const memoizedContinuityBuilderData = useMemo(
    () => <ContinuityBuilderData onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );
  const memoizedReadinessBuilderData = useMemo(
    () => <ReadinessBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );
  const memoizedOwnerRelianceBuilderData = useMemo(
    () => <OwnerRelianceBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );
  const memoizedTransitionBuilderData = useMemo(
    () => <TransitionBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );
  const memoizedWealthBuilderData = useMemo(
    () => <WealthBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );

  const memoizedAssetProtectionBuilderData = useMemo(
    () => <AssetProtectionBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );

  const memoizedValueEnhancementBuilderData = useMemo(
    () => <ValueEnhancementBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );

  const memoizedStakeholderAlignmentBuilderData = useMemo(
    () => (
      <StakeholderAlignmentBuilder onSelectionChange={handleSelectionChange} />
    ),
    [handleSelectionChange]
  );

  const memoizedBuyerTypeBuilderData = useMemo(
    () => <BuyerTypeBuilder onSelectionChange={handleSelectionChange} />,
    [handleSelectionChange]
  );

  const handleSendReportToBO = useCallback(() => {
    if (currentReport) {
      dispatch(sendReportToBO(currentReport.id, true));
    }
  }, [dispatch, currentReport]);

  useEffect(
    () => () => {
      dispatch(setCurrentReport(null));
      dispatch(setReports([]));
      dispatch(setIsReportCreated(false));
    },
    []
  );

  return (
    <div className='flex flex-col h-full gap-4'>
      <div className='flex justify-between items-center w-full'>
        <div className='flex gap-4 items-center'>
          <h1 className='text-2xl whitespace-nowrap font-semibold'>
            {isBusinessOwner ? 'Report' : 'Report Builder'}
          </h1>
          {reportLoading && <Spinner size='sm' />}
        </div>
        {isBusinessOwner ? (
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={handleDownload}
            disabled={isEditorEmpty}
          >
            Download PDF
          </Button>
        ) : (
          <div className='flex gap-2 justify-end'>
            <Button
              className='py-2 px-6'
              LeadingIcon={FaPaperPlane}
              leadingIconClassName='mr-2'
              onClick={handleSendReportToBO}
              disabled={!isReportCreated}
            >
              Send to Business Owner
            </Button>
            <Button
              className='py-2 px-6'
              LeadingIcon={FaDownload}
              leadingIconClassName='mr-2'
              onClick={handleDownload}
              disabled={isEditorEmpty}
            >
              Download PDF
            </Button>
            <Button
              className='py-2 px-6'
              onClick={() => setIsCreateModalOpen(true)}
              disabled={isEditorEmpty}
            >
              Create Report
            </Button>
          </div>
        )}
      </div>
      <div className='flex flex-col max-h-[calc(100%-3rem)] overflow-auto pr-2 scrollbar'>
        {pdfPages.map((page, index) => (
          <React.Fragment key={getKey(index)}>
            <div className='m-auto'>
              <div
                id={`report-page-${index}`}
                className='bg-white rounded-lg shadow-lg'
              >
                <div dangerouslySetInnerHTML={{ __html: page.innerHTML }} />
              </div>
            </div>
            {index < pdfPages.length - 1 && <div className='my-9' />}
          </React.Fragment>
        ))}
      </div>
      <div
        className={`flex h-full w-full gap-4 font-medium bg-white rounded-lg p-8 ${
          isBusinessOwner ? 'absolute -left-[9999px]' : ''
        }`}
      >
        <Editor
          ref={editorRef}
          onChange={handleEditorChange}
          disabled={reportLoading}
        />
        <ToolSidebar onToolSelect={handleToolSelect} disabled={reportLoading} />
      </div>

      <ToolDataPopup
        tool={selectedTool}
        onClose={handleClose}
        onSave={handleSave}
      >
        {selectedTool === AssessmentTools.BUSINESS_CONTINUITY &&
          memoizedContinuityBuilderData}
        {selectedTool === AssessmentTools.READINESS_ASSESSMENT &&
          memoizedReadinessBuilderData}
        {selectedTool === AssessmentTools.OWNER_RELIANCE &&
          memoizedOwnerRelianceBuilderData}
        {selectedTool === AssessmentTools.TRANSITION_OBJECTIVES &&
          memoizedTransitionBuilderData}
        {selectedTool === AssessmentTools.FINANCIAL_GAP_ANALYSIS &&
          memoizedWealthBuilderData}
        {selectedTool === AssessmentTools.ASSET_PROTECTION &&
          memoizedAssetProtectionBuilderData}
        {selectedTool === AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES &&
          memoizedValueEnhancementBuilderData}
        {selectedTool === AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING &&
          memoizedStakeholderAlignmentBuilderData}
        {selectedTool === AssessmentTools.BUSINESS_VALUATION && (
          <div>Tool Builder Under Development</div>
        )}
        {selectedTool === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE &&
          memoizedBuyerTypeBuilderData}
      </ToolDataPopup>
      <CreateReportModal
        visible={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateReport}
        loading={reportLoading}
      />
    </div>
  );
};

export default ReportBuilder;
