import React, { useCallback, useEffect, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentTools,
  RouteKey,
  UserRouteType,
  UserType,
  ValueEnhancementTabs,
} from 'types/enum';
import {
  EnhancementVEOs,
  idToQuestion,
  getTitle,
} from 'views/Value-Enhancement/ValueEnhancementConfig';
import TableValueReportBuilderComponent from 'views/Value-Enhancement/report/TableValueReportBuilderComponent';
import TableValueReportComponent from 'views/Value-Enhancement/report/TableValueReportComponent';

interface Props {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const ValueEnhancementBuilder: React.FC<Props> = ({ onSelectionChange }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);
  const [selectedEssentialVeos, setSelectedEssentialVeos] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedSupportingVeos, setSelectedSupportingVeos] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});
  const dispatch = useDispatch();

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
      dispatch(
        fetchComments(
          Number(id),
          AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES
        )
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const enhancementResponse = Object.entries(
    response?.assessment_response
  ).reduce<{
    essential_veos: EnhancementVEOs[];
    supporting_veos: EnhancementVEOs[];
  }>(
    (acc, category: any) => {
      Object.entries(category[1])?.forEach(([veoKey, veoData]: any) => {
        if (
          idToQuestion[veoKey].desired_answers.includes(
            veoData?.selected_option
          )
        ) {
          if (veoKey.includes('(E)')) {
            acc.essential_veos.push({ veo: veoKey, pillar: category[0] });
          } else {
            acc.supporting_veos.push({ veo: veoKey, pillar: category[0] });
          }
        }
      });
      return acc;
    },
    { essential_veos: [], supporting_veos: [] }
  );

  const onEssentialsCheckBoxClick = (
    selectedVEOs: string | string[],
    checkedAll?: boolean
  ) => {
    if (typeof selectedVEOs === 'object') {
      const obj = selectedVEOs?.reduce((acc, curr) => {
        acc[curr] = !!checkedAll;
        return acc;
      }, {} as { [key: string]: boolean });
      setSelectedEssentialVeos({ ...selectedEssentialVeos, ...obj });
    } else {
      setSelectedEssentialVeos({
        ...selectedEssentialVeos,
        [selectedVEOs]: !selectedEssentialVeos[selectedVEOs],
      });
    }
  };

  const onSupportingCheckBoxClick = (
    selectedVEOs: string | string[],
    checkedAll?: boolean
  ) => {
    if (typeof selectedVEOs === 'object') {
      const obj = selectedVEOs?.reduce((acc, curr) => {
        acc[curr] = !!checkedAll;
        return acc;
      }, {} as { [key: string]: boolean });
      setSelectedSupportingVeos({ ...selectedSupportingVeos, ...obj });
    } else {
      setSelectedSupportingVeos({
        ...selectedSupportingVeos,
        [selectedVEOs]: !selectedSupportingVeos[selectedVEOs],
      });
    }
  };

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const getSelectedDataForEditor = useCallback(() => {
    const essentialsSelectedData = enhancementResponse.essential_veos.filter(
      (e) => selectedEssentialVeos[e.veo]
    );

    const supportingSelectedData = enhancementResponse.supporting_veos.filter(
      (e) => selectedSupportingVeos[e.veo]
    );

    const renderedEssentialTable = essentialsSelectedData.length > 0 && (
      <TableValueReportComponent
        response={essentialsSelectedData}
        sectionTitle='The Essential VEOs listed here require your attention'
        className='!p-0'
      />
    );

    const renderedSupportingTable = supportingSelectedData.length > 0 && (
      <TableValueReportComponent
        response={supportingSelectedData}
        sectionTitle='The Supporting VEOs listed here require your attention'
        className='!p-0'
      />
    );

    // Group selected comments by pillar
    const groupedComments = Object.values(ValueEnhancementTabs).reduce(
      (acc, pillar) => {
        const pillarComments = comments
          .filter(
            (comment) =>
              comment.metadata?.screen === pillar &&
              selectedComments[comment.id.toString()]
          )
          .map((comment) => ({
            id: comment.id,
            comment: comment.comment,
            metadata: comment.metadata,
          }));

        if (pillarComments.length > 0) {
          acc[pillar] = pillarComments;
        }
        return acc;
      },
      {} as { [key: string]: any[] }
    );

    const renderDataForQuill = {
      veoData: {
        content: renderToString(
          <>
            {renderedEssentialTable}
            {renderedSupportingTable}
          </>
        ),
        type: 'VEO-Data',
        comments: Object.values(groupedComments).flat(),
      },
    };

    return renderDataForQuill;
  }, [
    selectedEssentialVeos,
    selectedSupportingVeos,
    selectedComments,
    comments,
    enhancementResponse,
  ]);

  useEffect(() => {
    onSelectionChange(getSelectedDataForEditor());
  }, [getSelectedDataForEditor, onSelectionChange]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  if (assessmentLoading) {
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-10 h-[calc(100vh-18rem)] overflow-y-auto pr-2 scrollbar'>
      <TableValueReportBuilderComponent
        selectedRows={selectedEssentialVeos}
        onCheckBoxClick={onEssentialsCheckBoxClick}
        response={enhancementResponse.essential_veos}
        sectionTitle='The Essential VEOs listed here require your attention'
      />
      <TableValueReportBuilderComponent
        selectedRows={selectedSupportingVeos}
        onCheckBoxClick={onSupportingCheckBoxClick}
        response={enhancementResponse.supporting_veos}
        sectionTitle='The Supporting VEOs listed here require your attention'
      />
      {comments.length > 0 ? (
        <div className='mt-2'>
          <span className='font-semibold text-lg mb-2 text-blue-01'>
            Comments:
          </span>
          {Object.values(ValueEnhancementTabs).map((pillar) => {
            const pillarComments = comments.filter(
              (comment) => comment.metadata?.screen === pillar
            );
            if (pillarComments.length === 0) return null;
            return (
              <div key={pillar} className='mb-4'>
                <h5 className='font-semibold mb-2'>{getTitle(pillar)}</h5>
                {pillarComments.map((comment) => (
                  <div key={comment.id} className='ml-4 mb-2 flex items-center'>
                    <Checkbox
                      onChange={() =>
                        handleCommentCheckboxChange(comment.id.toString())
                      }
                      value={selectedComments[comment.id.toString()] || false}
                      text={comment.comment as any}
                      className='!mt-0'
                    />
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      ) : null}
    </div>
  );
};

export default ValueEnhancementBuilder;
