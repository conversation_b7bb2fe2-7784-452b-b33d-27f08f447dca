import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { AssessmentTools, RouteKey, UserType } from 'types/enum';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { renderToString } from 'react-dom/server';
import { getQuestions } from 'views/TransitionObjectives/Questions';
import QuestionsTableComp from 'views/TransitionObjectives/QuestionsTableComp';
import { BusinessGoals } from 'shared-resources/types/TransitionObjectives.type';
import { QuestionAnswerTab } from 'views/TransitionObjectives/TransitionObjectiveReport';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import { getObjectives } from 'views/TransitionObjectives/TransitionObjectiveConfig';

interface TransitionBuilderProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const TransitionBuilder: React.FC<TransitionBuilderProps> = ({
  onSelectionChange,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [selectedQuestions, setSelectedQuestions] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});
  const [isCompleteOwner, setIsCompleteOwner] = useState(false);

  const loggedInUserData = useSelector(getUserData);
  const response = useSelector(getAssessmentReportResponse);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(
        fetchComments(Number(id), AssessmentTools.TRANSITION_OBJECTIVES)
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(() => {
    if ((response?.assessment_response as any)?.owners) {
      const ownershipData = JSON.parse(
        (response?.assessment_response as any)?.owners
      ).find(
        (owner: { name: string; ownership: string }) =>
          owner.name === response?.business_owner_data?.business_owner_name
      );

      setIsCompleteOwner(Number(ownershipData?.ownership) === 100);
    }
  }, [response]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  const handleCheckboxChange = useCallback((questionId: string) => {
    setSelectedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const renderQuestionContent = useCallback((questionObj: any, answer: any) => {
    if (typeof answer === 'object' && !Array.isArray(answer)) {
      return (
        <div className='flex flex-col relative w-full'>
          <span className='font-medium mb-2'>{questionObj.question}</span>
          <div className='w-full'>
            <QuestionsTableComp
              allowEditing={false}
              headers={questionObj.headers || ['']}
              tableValues={answer as BusinessGoals}
              objectives={getObjectives(questionObj)}
              setTableValues={() => {}}
              isReportView
              initialDynamicTableValues={answer}
            />
          </div>
          {answer?.stakeHolderNotes?.notes && (
            <div className='mt-2 w-full'>
              <span className='font-medium'>Notes:</span>
              <div className='rounded-lg border-gray-300 border-2 py-2 px-6 font-montserrat text-blue-01 mt-2'>
                {answer.stakeHolderNotes.notes}
              </div>
            </div>
          )}
        </div>
      );
    }
    return (
      <div className='relative w-full'>
        <QuestionAnswerTab
          question={questionObj.question}
          answer={answer}
          options={questionObj.options}
          questionNumber={questionObj.id}
        />
      </div>
    );
  }, []);

  const renderComments = useCallback(
    (questionId: string) => {
      const questionComments = comments.filter(
        (comment) => comment.metadata?.screen === questionId
      );

      if (questionComments.length === 0) return null;

      return (
        <div className='mt-4'>
          <span className='font-semibold mb-2'>Comments:</span>
          {questionComments.map((comment) => (
            <div key={comment.id} className='ml-4 mb-2 flex items-center'>
              <Checkbox
                onChange={() =>
                  handleCommentCheckboxChange(comment.id.toString())
                }
                value={selectedComments[comment.id.toString()] || false}
                text={comment.comment as any}
                className='!mt-0'
              />
            </div>
          ))}
        </div>
      );
    },
    [comments, selectedComments, handleCommentCheckboxChange]
  );

  const selectedData = useMemo(() => {
    const data: {
      [key: string]: { content: string; type: string; comments?: any[] };
    } = {};

    getQuestions(isCompleteOwner).forEach((questionObj) => {
      if (selectedQuestions[questionObj.id.toString()]) {
        const answer = (response?.assessment_response as any)?.[questionObj.id];
        if (answer) {
          const renderedComponent = renderToString(
            <div className='w-full'>
              {renderQuestionContent(questionObj, answer)}
            </div>
          );

          const questionComments = comments
            .filter(
              (comment) =>
                comment.metadata?.screen === questionObj.id.toString() &&
                selectedComments[comment.id.toString()]
            )
            .map((comment) => ({
              id: comment.id,
              comment: comment.comment,
              metadata: comment.metadata,
            }));

          data[questionObj.id.toString()] = {
            content: renderedComponent,
            type: 'transition-question',
            comments: questionComments,
          };
        }
      }
    });

    return data;
  }, [
    selectedQuestions,
    selectedComments,
    response?.assessment_response,
    renderQuestionContent,
    isCompleteOwner,
    comments,
  ]);

  useEffect(() => {
    onSelectionChange(selectedData);
  }, [selectedData, onSelectionChange]);

  if (assessmentLoading)
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-18rem)] overflow-y-auto scrollbar pr-4'>
      {getQuestions(isCompleteOwner).map((questionObj) => {
        const answer = (response?.assessment_response as any)?.[questionObj.id];
        if (answer) {
          return (
            <div key={questionObj.id} className='flex flex-col gap-4'>
              <div className='flex items-start gap-4'>
                <Checkbox
                  onChange={() =>
                    handleCheckboxChange(questionObj.id.toString())
                  }
                  value={selectedQuestions[questionObj.id.toString()] || false}
                />
                <div className='flex-1'>
                  {renderQuestionContent(questionObj, answer)}
                </div>
              </div>
              {renderComments(questionObj.id.toString())}
            </div>
          );
        }
        return null;
      })}
    </div>
  );
};

export default TransitionBuilder;
