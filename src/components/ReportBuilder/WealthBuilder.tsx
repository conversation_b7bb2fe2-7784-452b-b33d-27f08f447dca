import cx from 'classnames';
import _ from 'lodash';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserType } from 'types/enum';
import {
  ExpensesReportResponse,
  reportPagination,
} from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculatorConfig';
import {
  formatFinancialToolResponseData,
  FormattedToolResponse,
  getKeyTitle,
} from 'views/FinancialGapAnalysis/FinancialGapAnalysisConfig';
import ExpenseTableComponent from 'views/FinancialGapAnalysis/FinancialGapAnalysisReport/ExpenseTableComponent';
import ToolTableComponent from 'views/FinancialGapAnalysis/FinancialGapAnalysisReport/ToolTableComponent';
import { renderToString } from 'react-dom/server';
import { titleCaseAndRemoveUnderScoreOrHyphen } from 'utils/helpers/Helpers';

interface WealthBuilderProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments: any[] };
  }) => void;
}

const WealthBuilder: React.FC<WealthBuilderProps> = ({ onSelectionChange }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const response: any = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  const [selectedQuestions, setSelectedQuestions] = useState<
    Record<string, boolean>
  >({});
  const [selectedComments, setSelectedComments] = useState<
    Record<string, boolean>
  >({});
  const [assessmentToolResponse, setAssessmentToolResponse] = useState<
    FormattedToolResponse[][]
  >([]);
  const [expenseResponse, setExpenseResponse] = useState<
    ExpensesReportResponse[]
  >([]);

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(
        fetchComments(Number(id), AssessmentTools.FINANCIAL_GAP_ANALYSIS)
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    [dispatch]
  );

  useEffect(() => {
    if (!_.isEmpty(response.assessment_response)) {
      setAssessmentToolResponse(
        formatFinancialToolResponseData(response.assessment_response)
      );
      if (
        !_.isEmpty(response.assessment_response.expense_calculator_response)
      ) {
        setExpenseResponse(
          reportPagination(
            response.assessment_response.expense_calculator_response
          )
        );
      }
    }
  }, [response]);

  const handleCheckboxChange = useCallback((key: string) => {
    setSelectedQuestions((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const selectedData = useMemo(() => {
    const data = {} as {
      [key: string]: { content: string; type: string; comments: any[] };
    };

    assessmentToolResponse.forEach((toolResponse, index) => {
      if (toolResponse.length === 1) {
        const key = `tool-${index}`;
        if (selectedQuestions[key]) {
          const renderedComponent = renderToString(
            <ToolTableComponent isReportBuilder response={toolResponse} />
          );

          // Get filtered comments for this tool
          const toolComments = comments
            .filter(
              (comment) =>
                titleCaseAndRemoveUnderScoreOrHyphen(
                  comment.metadata?.screen
                ) === toolResponse[0].heading &&
                selectedComments[comment.id.toString()]
            )
            .map((comment) => ({
              id: comment.id,
              comment: comment.comment,
              metadata: comment.metadata,
            }));

          data[key] = {
            content: renderedComponent,
            type: 'financial-tool',
            comments: toolComments,
          };
        }
      } else {
        toolResponse.forEach((toolItem, subIndex) => {
          const key = `tool-${index}-${subIndex}`;
          if (selectedQuestions[key]) {
            const renderedComponent = renderToString(
              <ToolTableComponent isReportBuilder response={[toolItem]} />
            );

            // Get filtered comments for this tool item
            const toolItemComments = comments
              .filter(
                (comment) =>
                  titleCaseAndRemoveUnderScoreOrHyphen(
                    comment.metadata?.screen
                  ) === toolItem.heading &&
                  selectedComments[comment.id.toString()]
              )
              .map((comment) => ({
                id: comment.id,
                comment: comment.comment,
                metadata: comment.metadata,
              }));

            data[key] = {
              content: renderedComponent,
              type: 'financial-tool',
              comments: toolItemComments,
            };
          }
        });
      }
    });

    expenseResponse.forEach((res, index) => {
      Object.keys(res).forEach((key, subIndex) => {
        const expenseKey = `expense-${index}-${subIndex}`;
        if (selectedQuestions[expenseKey]) {
          const renderedComponent = renderToString(
            <ExpenseTableComponent
              isReportBuilder
              response={
                {
                  [key]: (res as { [key: string]: any })[key],
                } as ExpensesReportResponse
              }
            />
          );

          // Get filtered comments for this expense
          const expenseComments = comments
            .filter(
              (comment) =>
                comment.metadata?.screen === key &&
                selectedComments[comment.id.toString()]
            )
            .map((comment) => ({
              id: comment.id,
              comment: comment.comment,
              metadata: comment.metadata,
            }));

          data[expenseKey] = {
            content: renderedComponent,
            type: 'expense',
            comments: expenseComments,
          };
        }
      });
    });

    return data;
  }, [
    selectedQuestions,
    selectedComments,
    assessmentToolResponse,
    expenseResponse,
    comments,
  ]);

  useEffect(() => {
    onSelectionChange(selectedData);
  }, [selectedData, onSelectionChange]);

  if (assessmentLoading)
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );

  return (
    <div
      className={cx(
        'h-[calc(100vh-18rem)] bg-white overflow-y-scroll scrollbar rounded-xl'
      )}
    >
      <div className=''>
        {assessmentToolResponse.map((toolResponse, index) => (
          <div key={`${toolResponse}`}>
            {toolResponse.length === 1 ? (
              <div className='pt-2 space-x-4 flex flex-col w-9/10'>
                <div className='flex gap-2 items-start'>
                  <Checkbox
                    onChange={() => handleCheckboxChange(`tool-${index}`)}
                    value={!!selectedQuestions[`tool-${index}`]}
                    className='!mt-0'
                  />
                  <ToolTableComponent isReportBuilder response={toolResponse} />
                </div>
                <div className='mt-4'>
                  {comments.filter(
                    (comment) =>
                      titleCaseAndRemoveUnderScoreOrHyphen(
                        comment.metadata?.screen
                      ) === toolResponse[0]?.heading
                  ).length > 0 ? (
                    <span className='font-semibold mb-2'>Comments:</span>
                  ) : null}
                  {comments
                    .filter(
                      (comment) =>
                        titleCaseAndRemoveUnderScoreOrHyphen(
                          comment.metadata?.screen
                        ) === toolResponse[0]?.heading
                    )
                    .map((comment) => (
                      <div
                        key={comment.id}
                        className='ml-4 mb-2 flex items-center'
                      >
                        <Checkbox
                          onChange={() =>
                            handleCommentCheckboxChange(comment.id.toString())
                          }
                          value={
                            selectedComments[comment.id.toString()] || false
                          }
                          text={comment.comment as any}
                          className='!mt-0'
                        />
                      </div>
                    ))}
                </div>
              </div>
            ) : (
              <>
                {toolResponse.map((toolItem, subIndex) => (
                  <div
                    className='pt-2 flex flex-col space-x-4 w-9/10'
                    key={toolItem.heading}
                  >
                    <div className='flex gap-2 items-start'>
                      <Checkbox
                        onChange={() =>
                          handleCheckboxChange(`tool-${index}-${subIndex}`)
                        }
                        value={!!selectedQuestions[`tool-${index}-${subIndex}`]}
                        className='!mt-0'
                      />
                      <ToolTableComponent
                        isReportBuilder
                        response={[toolItem]}
                      />
                    </div>
                    <div className='mt-4'>
                      {comments.filter(
                        (comment) =>
                          titleCaseAndRemoveUnderScoreOrHyphen(
                            comment.metadata?.screen
                          ) === toolItem.heading
                      ).length > 0 ? (
                        <span className='font-semibold mb-2'>Comments:</span>
                      ) : null}
                      {comments
                        .filter(
                          (comment) =>
                            titleCaseAndRemoveUnderScoreOrHyphen(
                              comment.metadata?.screen
                            ) === toolItem.heading
                        )
                        .map((comment) => (
                          <div
                            key={comment.id}
                            className='ml-4 mb-2 flex items-center'
                          >
                            <Checkbox
                              onChange={() =>
                                handleCommentCheckboxChange(
                                  comment.id.toString()
                                )
                              }
                              value={
                                selectedComments[comment.id.toString()] || false
                              }
                              text={comment.comment as any}
                              className='!mt-0'
                            />
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        ))}
      </div>
      <div className=''>
        {expenseResponse.map((res, index) => (
          <div key={`${res}`}>
            <div className='pt-16 w-9/10'>
              {Object.keys(res)
                .slice(0, -1)
                .map((key, subIndex) => (
                  <div
                    className='flex flex-col space-x-4 items-start'
                    key={`${key}`}
                  >
                    <div className='flex items-start'>
                      <Checkbox
                        onChange={() =>
                          handleCheckboxChange(`expense-${index}-${subIndex}`)
                        }
                        value={
                          !!selectedQuestions[`expense-${index}-${subIndex}`]
                        }
                        text={getKeyTitle(key) as any}
                        className='!mt-0 mr-2'
                      />
                      <ExpenseTableComponent
                        isReportBuilder
                        response={
                          {
                            [key]: (res as { [key: string]: any })[key],
                          } as ExpensesReportResponse
                        }
                      />
                    </div>
                    <div className='mt-4'>
                      {comments.filter(
                        (comment) => comment.metadata?.screen === key
                      )?.length > 0 ? (
                        <span className='font-semibold mb-2'>Comments:</span>
                      ) : null}
                      {comments
                        .filter((comment) => comment.metadata?.screen === key)
                        .map((comment) => (
                          <div
                            key={comment.id}
                            className='ml-4 mb-2 flex items-center'
                          >
                            <Checkbox
                              onChange={() =>
                                handleCommentCheckboxChange(
                                  comment.id.toString()
                                )
                              }
                              value={
                                selectedComments[comment.id.toString()] || false
                              }
                              text={comment.comment as any}
                              className='!mt-0'
                            />
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WealthBuilder;
