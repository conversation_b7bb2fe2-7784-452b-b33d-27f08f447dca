import React, { useCallback, useEffect, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserType } from 'types/enum';
import {
  BuyerTypeScreens,
  BuyerReportScreens,
  screenToQuestionMapping,
  convertObjectToFormattedResponse,
  SECTION_TITLES,
  TABLE_COLUMNS,
  ALREADY_IDENTIFIED_FIELDS,
  NOT_IDENTIFIED_FIELDS,
  DEAL_STRUCTURE_QUESTIONS,
} from 'views/BuyerType/BuyerTypeConfig';
import BuyerTypeTableComponent from 'views/BuyerType/BuyerTypeTableComponent';
import NotIndentifiedTableReport from 'views/BuyerType/BuyerTypeScreens/NotIndentifiedTableReport';

interface Props {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const BUYER_TYPE_SECTION = 'buyer-type';

// Helper Components
const QuestionWithCheckbox: React.FC<{
  question: string;
  value: string;
  checkboxKey: string;
  isChecked: boolean;
  onCheckboxChange: (key: string) => void;
}> = ({ question, value, checkboxKey, isChecked, onCheckboxChange }) => (
  <div className='flex flex-col space-y-3'>
    <div className='flex items-start gap-4'>
      <Checkbox
        onChange={() => onCheckboxChange(checkboxKey)}
        value={isChecked}
        className='!mt-0'
      />
      <div className='w-full'>
        <h1>{question}</h1>
        <div className='border w-full rounded-lg px-8 py-4 font-medium h-[5rem] mt-2'>
          {value ?? 'No Data'}
        </div>
      </div>
    </div>
  </div>
);

const CommentSection: React.FC<{
  comments: any[];
  screen: BuyerReportScreens;
  selectedComments: Record<string, boolean>;
  onCommentChange: (id: string) => void;
  screenMapping: Record<BuyerReportScreens, string[]>;
}> = ({
  comments,
  screen,
  selectedComments,
  onCommentChange,
  screenMapping,
}) => {
  const filteredComments = comments.filter((comment) =>
    screenMapping[screen].includes(comment.metadata?.screen as BuyerTypeScreens)
  );

  if (filteredComments.length === 0) return null;

  return (
    <div className='mt-4 ml-4'>
      <span className='font-semibold mb-2'>Comments:</span>
      {filteredComments.map((comment) => (
        <div key={comment.id} className='ml-4 mb-2 flex items-center'>
          <Checkbox
            onChange={() => onCommentChange(comment.id.toString())}
            value={selectedComments[comment.id.toString()] || false}
            text={<div>{comment.comment}</div>}
            className='!mt-0'
          />
        </div>
      ))}
    </div>
  );
};

// Main Component
const BuyerTypeBuilder: React.FC<Props> = ({ onSelectionChange }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  const [selectedSections, setSelectedSections] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(
        fetchComments(Number(id), AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE)
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    [dispatch]
  );

  const handleCheckboxChange = useCallback((section: string) => {
    setSelectedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const getSectionTitle = (screen: BuyerReportScreens) =>
    SECTION_TITLES[screen];

  const renderScreen1 = useCallback(
    (res: any) => {
      const alreadyIdentifiedData =
        res[BuyerTypeScreens.ALREADY_IDENTIFIED] || {};

      return (
        <div className='px-10'>
          <div className='flex flex-col gap-4'>
            <div className='flex items-start gap-4'>
              <Checkbox
                onChange={() =>
                  handleCheckboxChange('buyer_type_understanding')
                }
                value={selectedSections.buyer_type_understanding || false}
                text={
                  <div className='font-medium'>Buyer Type Understanding</div>
                }
                className='!mt-0'
              />
            </div>
            <BuyerTypeTableComponent
              response={convertObjectToFormattedResponse(
                res[BuyerTypeScreens.TYPE_OF_BUYER],
                TABLE_COLUMNS.BUYER_TYPE
              )}
              heading='Buyer Type'
              text='How much do you know about the following types of buyers? Record your understanding of each using a scale of 1 to 6 where 1 indicates that you know almost nothing and 6 means you totally understand the buyer type.'
            />
          </div>

          <div className='flex flex-col py-10 space-y-8'>
            {ALREADY_IDENTIFIED_FIELDS.map(({ key, question }) => (
              <QuestionWithCheckbox
                key={key}
                question={question}
                value={
                  alreadyIdentifiedData[key]?.includes('_')
                    ? alreadyIdentifiedData[key]
                        ?.replace(/_/g, ' ')
                        .replace(/\b\w/g, (char: string) => char.toUpperCase())
                    : alreadyIdentifiedData[key]
                }
                checkboxKey={key}
                isChecked={selectedSections[key] || false}
                onCheckboxChange={handleCheckboxChange}
              />
            ))}
          </div>
        </div>
      );
    },
    [selectedSections, handleCheckboxChange]
  );

  const renderScreen2 = useCallback(
    (res: any) => (
      <div className='mt-8'>
        <div className='flex flex-col gap-4'>
          <div className='flex items-start gap-4'>
            <Checkbox
              onChange={() => handleCheckboxChange('not_identified_table')}
              value={selectedSections.not_identified_table || false}
              text={<div className='font-medium'>Not Identified Table</div>}
              className='!mt-0'
            />
          </div>
          <NotIndentifiedTableReport data={res} />
        </div>

        <div className='px-10 mt-6 flex flex-col gap-4'>
          <h1 className='text-2xl font-medium'>Buyer Type (Not Identified)</h1>
          {NOT_IDENTIFIED_FIELDS.map(({ key, question }) => (
            <QuestionWithCheckbox
              key={key}
              question={question}
              value={res[BuyerTypeScreens.NOT_IDENTIFIED]?.[key] || 'No Data'}
              checkboxKey={key}
              isChecked={selectedSections[key] || false}
              onCheckboxChange={handleCheckboxChange}
            />
          ))}
        </div>
      </div>
    ),
    [selectedSections, handleCheckboxChange]
  );

  const renderScreen3 = useCallback(
    (res: any) => {
      const dealStructure2Data = res[BuyerTypeScreens.DEAL_STRUCTURE_2] || {};

      return (
        <div className='px-10 flex flex-col gap-4'>
          <div className='flex items-start gap-4'>
            <Checkbox
              onChange={() => handleCheckboxChange('deal_structure_table')}
              value={selectedSections.deal_structure_table || false}
              text={
                <div className='font-medium'>Deal Structure Understanding</div>
              }
              className='!mt-0'
            />
          </div>
          <BuyerTypeTableComponent
            isDealStructure
            response={convertObjectToFormattedResponse(
              res[BuyerTypeScreens.DEAL_STRUCTURE],
              TABLE_COLUMNS.DEAL_STRUCTURE,
              true
            )}
            heading='Deal Structure'
            text='Record your understanding of the following questions/concepts using a scale of 1 to 6 where 1 indicates that you know almost nothing and 6 means you totally understand the difference.'
          />

          <div className='flex flex-col space-y-4'>
            {Object.entries(DEAL_STRUCTURE_QUESTIONS).map(([key, question]) => (
              <QuestionWithCheckbox
                key={key}
                question={question}
                value={dealStructure2Data[key] || 'No Data'}
                checkboxKey={`deal_structure_${key}`}
                isChecked={selectedSections[`deal_structure_${key}`] || false}
                onCheckboxChange={handleCheckboxChange}
              />
            ))}
          </div>
        </div>
      );
    },
    [selectedSections, handleCheckboxChange]
  );

  const renderScreenContent = useCallback(
    (screen: BuyerReportScreens, data: any) => {
      switch (screen) {
        case BuyerReportScreens.SCREEN_1:
          return renderScreen1(data);
        case BuyerReportScreens.SCREEN_2:
          return renderScreen2(data);
        case BuyerReportScreens.SCREEN_3:
          return renderScreen3(data);
        default:
          return null;
      }
    },
    [renderScreen1, renderScreen2, renderScreen3]
  );

  const assessmentResponse = response?.assessment_response as any;

  const getSelectedData = useCallback(() => {
    if (!response?.assessment_response) return {};

    const data: {
      [key: string]: { content: string; type: string; comments: any[] };
    } = {};

    // Create a map of selected comments by screen
    const selectedCommentsMap = comments
      .filter((comment) => selectedComments[comment.id.toString()])
      .reduce((acc, comment) => {
        const screen = comment.metadata?.screen;
        if (screen) {
          if (!acc[screen]) {
            acc[screen] = [];
          }
          acc[screen].push({
            id: comment.id,
            comment: comment.comment,
            metadata: comment.metadata,
          });
        }
        return acc;
      }, {} as Record<string, any[]>);

    // Group comments by main sections
    const groupedComments = {
      buyerTypeSection: [
        ...(selectedCommentsMap[BuyerTypeScreens.TYPE_OF_BUYER] || []),
        ...(selectedCommentsMap[BuyerTypeScreens.ALREADY_IDENTIFIED] || []),
      ],
      notIdentifiedSection: [
        ...(selectedCommentsMap[BuyerTypeScreens.NOT_IDENTIFIED_TABLE] || []),
        ...(selectedCommentsMap[BuyerTypeScreens.NOT_IDENTIFIED] || []),
      ],
      dealStructureSection: [
        ...(selectedCommentsMap[BuyerTypeScreens.DEAL_STRUCTURE] || []),
        ...(selectedCommentsMap[BuyerTypeScreens.DEAL_STRUCTURE_2] || []),
      ],
    };

    // Handle Buyer Type Understanding and Already Identified
    data.buyer_type_understanding = {
      content: selectedSections?.buyer_type_understanding
        ? renderToString(
            <div className='flex flex-col gap-4'>
              <h2 className='text-xl font-semibold'>
                Buyer Type Understanding
              </h2>
              <BuyerTypeTableComponent
                response={convertObjectToFormattedResponse(
                  assessmentResponse[BuyerTypeScreens.TYPE_OF_BUYER],
                  TABLE_COLUMNS.BUYER_TYPE
                )}
                heading='Buyer Type'
                text='How much do you know about the following types of buyers?'
              />
            </div>
          )
        : '',
      type: BUYER_TYPE_SECTION,
      comments: groupedComments.buyerTypeSection,
    };

    // Handle Already Identified Data
    const alreadyIdentifiedData =
      assessmentResponse[BuyerTypeScreens.ALREADY_IDENTIFIED] || {};

    const formatBuyerTypeValue = (key: string, value: string | undefined) => {
      if (!value) return 'No data';
      if (key === 'buyer_type') {
        return value
          .replace(/_/g, ' ')
          .replace(/\b\w/g, (char: string) => char.toUpperCase());
      }
      return value;
    };
    ALREADY_IDENTIFIED_FIELDS.forEach(({ key, question }) => {
      if (selectedSections[key]) {
        data[key] = {
          content: renderToString(
            <div className='flex flex-col gap-4'>
              <h3 className='font-medium'>{question}</h3>
              <div className='border rounded-lg px-8 py-4 font-medium'>
                {formatBuyerTypeValue(key, alreadyIdentifiedData[key])}
              </div>
            </div>
          ),
          type: BUYER_TYPE_SECTION,
          comments: [],
        };
      }
    });

    // Handle Not Identified Table
    data.not_identified_table = {
      content: selectedSections?.not_identified_table
        ? renderToString(
            <div className='flex flex-col gap-4'>
              <h2 className='text-xl font-semibold'>Not Identified Table</h2>
              <NotIndentifiedTableReport data={response.assessment_response} />
            </div>
          )
        : '',
      type: BUYER_TYPE_SECTION,
      comments: groupedComments.notIdentifiedSection,
    };

    // Handle Not Identified Details
    const notIdentifiedData =
      assessmentResponse[BuyerTypeScreens.NOT_IDENTIFIED] || {};
    NOT_IDENTIFIED_FIELDS.forEach(({ key, question }) => {
      if (selectedSections[key]) {
        data[key] = {
          content: renderToString(
            <div className='flex flex-col gap-4'>
              <h3 className='font-medium'>{question}</h3>
              <div className='border rounded-lg px-8 py-4 font-medium'>
                {notIdentifiedData[key] || 'No Data'}
              </div>
            </div>
          ),
          type: BUYER_TYPE_SECTION,
          comments: [],
        };
      }
    });

    // Handle Deal Structure Table
    data.deal_structure_table = {
      content: selectedSections?.deal_structure_table
        ? renderToString(
            <div className='flex flex-col gap-4'>
              <h2 className='text-xl font-semibold'>
                Deal Structure Understanding
              </h2>
              <BuyerTypeTableComponent
                isDealStructure
                response={convertObjectToFormattedResponse(
                  assessmentResponse[BuyerTypeScreens.DEAL_STRUCTURE],
                  TABLE_COLUMNS.DEAL_STRUCTURE,
                  true
                )}
                heading='Deal Structure'
                text='Record your understanding of the following questions/concepts using a scale of 1 to 6 where 1 indicates that you know almost nothing and 6 means you totally understand the difference.'
              />
            </div>
          )
        : '',
      type: BUYER_TYPE_SECTION,
      comments: groupedComments.dealStructureSection,
    };

    // Handle Deal Structure Details
    const dealStructure2Data =
      assessmentResponse[BuyerTypeScreens.DEAL_STRUCTURE_2] || {};
    Object.entries(DEAL_STRUCTURE_QUESTIONS).forEach(([key, question]) => {
      if (selectedSections[`deal_structure_${key}`]) {
        data[`deal_structure_${key}`] = {
          content: renderToString(
            <div className='flex flex-col gap-4'>
              <h3 className='font-medium'>{question}</h3>
              <div className='border rounded-lg px-8 py-4 font-medium'>
                {(dealStructure2Data[key] as string) ?? 'No Data'}
              </div>
            </div>
          ),
          type: BUYER_TYPE_SECTION,
          comments: [],
        };
      }
    });

    return data;
  }, [
    response?.assessment_response,
    selectedSections,
    selectedComments,
    comments,
  ]);

  useEffect(() => {
    const selectedData = getSelectedData();
    if (Object.keys(selectedData).length > 0) {
      onSelectionChange(selectedData);
    }
  }, [getSelectedData, onSelectionChange]);

  if (assessmentLoading) {
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-18rem)] overflow-y-auto scrollbar pr-4'>
      {Object.values(BuyerReportScreens).map((screen) => (
        <div key={screen} className='mb-8'>
          <h2 className='text-xl font-semibold mb-4'>
            {getSectionTitle(screen)}
          </h2>
          {renderScreenContent(screen, response?.assessment_response)}
          <CommentSection
            comments={comments}
            screen={screen}
            selectedComments={selectedComments}
            onCommentChange={handleCommentCheckboxChange}
            screenMapping={screenToQuestionMapping}
          />
        </div>
      ))}
    </div>
  );
};

export default BuyerTypeBuilder;
