import { User } from 'models/entities/User';
import { RouteKey, UserRouteType, UserType } from 'types/enum';
import {
  BusinessReadiness,
  FinanceReadiness,
  PlanningReadiness,
  ReadinessAssessmentTitle,
  TransitionKnowledge,
  TransitionObjectiveReadiness,
} from 'utils/Readiness-Assessment/ReadinessAssessmentEnums';
import {
  BUSINESS_READINESS_QUESTIONS_DATA,
  FINANCE_QUESTIONS_DATA,
  PLANNING_READINESS_QUESTIONS_DATA,
  QuestionKeys,
  TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
  TRANSITION_OBJECTIVE_QUESTIONS_DATA,
  initialTabData,
} from 'views/contents/Readiness-Assessment/readinessAssessment.constant';

export const getQuestions = (currentTabValue: string | number) => {
  switch (currentTabValue) {
    case ReadinessAssessmentTitle.BUSINESS_READINESS:
      return BUSINESS_READINESS_QUESTIONS_DATA;
    case ReadinessAssessmentTitle.FINANCE:
      return FINANCE_QUESTIONS_DATA;
    case ReadinessAssessmentTitle.TRANSITION_OBJECTIVES:
      return TRANSITION_OBJECTIVE_QUESTIONS_DATA;
    case ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE:
      return TRANSITION_KNOWLEDGE_QUESTIONS_DATA;
    case ReadinessAssessmentTitle.PLANNING:
      return PLANNING_READINESS_QUESTIONS_DATA;
    default:
      return BUSINESS_READINESS_QUESTIONS_DATA;
  }
};
export interface AnswerData {
  label: string | number;
  value: string | number;
}

export const handleNextTabSwitch = (
  currentTabValue: { tab: string | number },
  setCurrentTabValue: (tab: { tab: string | number }) => void,
  handleFormSubmit: (arg: boolean) => void
): void => {
  switch (currentTabValue.tab) {
    case ReadinessAssessmentTitle.BUSINESS_READINESS:
      setCurrentTabValue({ tab: ReadinessAssessmentTitle.FINANCE });
      break;
    case ReadinessAssessmentTitle.FINANCE:
      setCurrentTabValue({
        tab: ReadinessAssessmentTitle.TRANSITION_OBJECTIVES,
      });
      break;
    case ReadinessAssessmentTitle.TRANSITION_OBJECTIVES:
      setCurrentTabValue({
        tab: ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE,
      });
      break;
    case ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE:
      setCurrentTabValue({ tab: ReadinessAssessmentTitle.PLANNING });
      break;
    case ReadinessAssessmentTitle.PLANNING:
      handleFormSubmit(false);
      return;
    default:
      setCurrentTabValue({ tab: ReadinessAssessmentTitle.BUSINESS_READINESS });
  }
};

export const handleBackTabSwitch = (
  currentTabValue: { tab: string | number },
  navigate: any,
  setCurrentTabValue: (tab: { tab: string | number }) => void,
  loggedInUserData?: User | undefined
): void => {
  let newTabValue = ReadinessAssessmentTitle.BUSINESS_READINESS;

  switch (currentTabValue.tab) {
    case ReadinessAssessmentTitle.BUSINESS_READINESS:
      if (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN) {
        navigate(`/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`);
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT_DASHBOARD}`
        );
      }
      break;
    case ReadinessAssessmentTitle.FINANCE:
      newTabValue = ReadinessAssessmentTitle.BUSINESS_READINESS;
      break;
    case ReadinessAssessmentTitle.TRANSITION_OBJECTIVES:
      newTabValue = ReadinessAssessmentTitle.FINANCE;
      break;
    case ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE:
      newTabValue = ReadinessAssessmentTitle.TRANSITION_OBJECTIVES;
      break;
    case ReadinessAssessmentTitle.PLANNING:
      newTabValue = ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE;
      break;
    default:
      break;
  }

  setCurrentTabValue({
    tab: newTabValue,
  });
};

export const getEnumValue = (enumKey: string): string | number => {
  switch (enumKey) {
    case 'BUSINESS_READINESS':
      return ReadinessAssessmentTitle.BUSINESS_READINESS;
    case 'FINANCE':
      return ReadinessAssessmentTitle.FINANCE;
    case 'TRANSITION_OBJECTIVES':
      return ReadinessAssessmentTitle.TRANSITION_OBJECTIVES;
    case 'TRANSITION_KNOWLEDGE':
      return ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE;
    case 'PLANNING':
      return ReadinessAssessmentTitle.PLANNING;
    default:
      return ReadinessAssessmentTitle.BUSINESS_READINESS;
  }
};

export const updateTabAndData = (
  response: any,
  setResponses: any,
  setCurrentTabValue: any,
  setTabData: any,
  setNextButtonClicked: any,
  nextButtonClicked: any
) => {
  setResponses(response || {});
  const responseKeys = Object?.keys(response || {});
  let tab: ReadinessAssessmentTitle =
    ReadinessAssessmentTitle.BUSINESS_READINESS;

  const enums = [
    BusinessReadiness,
    FinanceReadiness,
    TransitionObjectiveReadiness,
    TransitionKnowledge,
    PlanningReadiness,
  ];

  if (responseKeys.length > 0) {
    enums.some((enumType) => {
      const answeredCount = responseKeys.filter((key) =>
        Object.values(enumType).includes(key as keyof typeof enumType)
      ).length;

      if (
        answeredCount < 5 ||
        (answeredCount === 5 && enumType === PlanningReadiness)
      ) {
        tab = Object.keys(ReadinessAssessmentTitle)[
          enums.indexOf(enumType)
        ] as ReadinessAssessmentTitle;

        return true;
      }
      return false;
    });

    if (tab === ReadinessAssessmentTitle.BUSINESS_READINESS) {
      tab = ReadinessAssessmentTitle.FINANCE;
    }
  }

  const tabb = getEnumValue(tab);
  setCurrentTabValue({ tab: tabb });

  const updatedTabData = initialTabData.map((tabs: any) => {
    const relatedQuestions = getQuestions(tabs.id).map(
      (question: any) => question.id as string as QuestionKeys
    );
    const completedQuestions = responseKeys.filter((key) =>
      relatedQuestions.includes(key as QuestionKeys)
    );
    const isActive = completedQuestions.length > 0;
    const isCompleted = completedQuestions.length === relatedQuestions.length;
    return {
      ...tabs,
      isActive,
      isCompleted,
    };
  });

  setTabData(updatedTabData);
  setNextButtonClicked(!nextButtonClicked);
};

export const findQuestionById = (key: any) => {
  // Combine all question data into one array
  const allQuestionsData = [
    ...BUSINESS_READINESS_QUESTIONS_DATA,
    ...FINANCE_QUESTIONS_DATA,
    ...TRANSITION_OBJECTIVE_QUESTIONS_DATA,
    ...TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
    ...PLANNING_READINESS_QUESTIONS_DATA,
  ];
  // Find the question with the matching key (ID)
  const question = allQuestionsData.find((item) => item.id === key);
  // Return the question if found, otherwise return a not found message
  return question ? question.ques : 'Question with the given ID not found.';
};
