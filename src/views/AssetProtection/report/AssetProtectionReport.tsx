import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { FaDownload } from 'react-icons/fa';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import IntroductionPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/IntrodunctionPage';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { formatCurrency } from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculatorConfig';
import { titleCase } from 'utils/helpers/Helpers';
import {
  InsuranceTypeheaderOptions,
  Screen3Keys,
  Screen7Keys,
  Screen8TableResponseType,
  getQuestionBykey,
} from '../AssetProtectionConfig';
import DisclaimerPage from '../../Common/Disclaimer';

const AssetProtectionReport: React.FC = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);

  const dispatch = useDispatch();

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.ASSET_PROTECTION,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.ASSET_PROTECTION,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const filterResponse = (obj: Record<string, any>): Record<string, string> =>
    Object.entries(obj).reduce<Record<string, string>>((acc, [key, value]) => {
      if (key !== 'screen8' && typeof value === 'object') {
        Object.entries(value).forEach(([k, v]) => {
          if (v && typeof v === 'string') acc[k] = v;
        });
      }
      return acc;
    }, {});

  const convertToArrayOfObjects = (
    inputObj: Record<string, string>
  ): Record<string, string>[] =>
    Object.entries(inputObj).reduce((acc, [key, value], index) => {
      const objIndex = Math.floor((index - 8) / 11) + 1;
      if (!acc[objIndex]) acc[objIndex] = {};
      acc[objIndex][key] = titleCase(value) as string;

      return acc;
    }, [] as Record<string, string>[]);

  const filteredResponse = filterResponse(response?.assessment_response);
  const responseArr = convertToArrayOfObjects(filteredResponse);
  const tableResponse = (response?.assessment_response as any)?.screen8;

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];
    responseArr.forEach((res, index) => {
      reportPages.push(document.getElementById(`asset-page-${index}`));
    });
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `AssetProtectionReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => {
        setIsDownloading(false);
      },
      'portrait'
    );
  };

  const pageBreak = <div className='my-9' />;

  const displayPage = (res: Record<string, string>) =>
    Object.entries(res)?.map(([key, value]) => {
      if (key === Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE) {
        return (
          <div key={key} className='flex flex-col gap-4'>
            <div className='flex flex-col gap-2 mt-4 w-full'>
              <span className='font-semibold'>{getQuestionBykey(key)}</span>
              <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
                {value}
              </div>
            </div>
            {/* table */}
            <div className='pr-1 mt-3 pb-2'>
              <div
                className={`bg-blue-01 z-10 tracking-[0.07rem] grid grid-cols-4 text-white
            rounded-t-md  text-[0.9rem]`}
              >
                {InsuranceTypeheaderOptions.map((h, index) => (
                  <div
                    key={h}
                    className={`px-5  flex items-center border-r border-gray-02 justify-center py-1 h-full ${
                      index === 0 && 'rounded-tl-md'
                    } ${index === 3 && ' border-r-0 rounded-t-md'} `}
                  >
                    <span className='text-center'>{h}</span>
                  </div>
                ))}
              </div>
              <div>
                {Object.keys(tableResponse).map((tableKey) => (
                  <div
                    key={tableKey}
                    className={classNames('grid grid-cols-4  text-[0.9rem] ')}
                  >
                    <div
                      className={`px-5
                 flex items-center border-b border-l py-1 border-r border-gray-02`}
                    >
                      <span className='text-xs xl:text-sm '>{tableKey}</span>
                    </div>

                    <div className='flex py-2 justify-center border-b border-r border-gray-02 '>
                      <button
                        className={`py-1 px-5 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                          tableResponse[
                            tableKey as keyof Screen8TableResponseType
                          ]?.value === true && 'bg-blue-01'
                        }`}
                        onClick={() => {}}
                      >
                        Yes
                      </button>
                      <button
                        className={`py-1 px-5 rounded-r-xl border-2 border-gray-300 ${
                          tableResponse[
                            tableKey as keyof Screen8TableResponseType
                          ]?.value === false && 'bg-blue-01'
                        } `}
                        onClick={() => {}}
                      >
                        No
                      </button>
                    </div>

                    <div
                      className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
                    >
                      <div className='flex gap-2 items-center w-full'>
                        <span>$</span>
                        {formatCurrency(
                          tableResponse[
                            tableKey as keyof Screen8TableResponseType
                          ].coverage_amount
                        ).replace('$', '')}
                      </div>
                    </div>

                    <div
                      className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
                    >
                      <div className='flex gap-2 items-center w-full'>
                        <span>$</span>
                        {formatCurrency(
                          tableResponse[
                            tableKey as keyof Screen8TableResponseType
                          ].recommended_coverage
                        ).replace('$', '')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      }
      if (key === Screen3Keys.owner_type) return null;
      return (
        <div key={key} className='flex flex-col gap-2 mt-4 w-full'>
          <span className='font-semibold'>{getQuestionBykey(key)}</span>
          <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
            {value}
          </div>
        </div>
      );
    });
  if (assessmentLoading) {
    return <Spinner customClassName='' spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
            disabled={isDownloading}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col h-[calc(100%-3.75rem)] overflow-y-auto pr-2 scrollbar'>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Asset Protection Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone ?? ''}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <IntroductionPage
              userName={response?.business_owner_data?.business_owner_name}
              pageContent={
                <div>
                  Asset Protection is often under-appreciated and overlooked in
                  many businesses. The questionnaire that you completed covered
                  a lot of dimensions of asset protection: business continuity,
                  employee agreements, employee incentive plans, buy-sell
                  agreements, all types of insurance, tax minimization, safety,
                  economic threats, IP protection, copyright protection,
                  cybersecurity, financial controls, theft, disaster
                  preparedness, sexual harassment, and employee discrimination.
                  <br />
                  <br />
                  This Report contains all of your responses to the Asset
                  Protection questionnaire. Your responses that require
                  attention are listed first as those require your immediate
                  attention. The balance of your responses are listed at the end
                  for you to monitor over time to be sure that they are all kept
                  current.
                </div>
              }
              classname='pt-64 px-12'
            />
          </LightThemeReportWrapper>
        </div>

        {responseArr.map((res, index) => (
          <>
            {pageBreak}
            <div className='m-auto'>
              <LightThemeReportWrapper id={`asset-page-${index}`}>
                <div className='p-32'>{displayPage(res) as any}</div>
              </LightThemeReportWrapper>
            </div>
          </>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default AssetProtectionReport;
