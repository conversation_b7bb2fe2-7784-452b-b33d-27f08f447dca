import React, { useEffect, useState } from 'react';
import cx from 'classnames';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { UserType, AssessmentTools, RouteKey } from 'types/enum';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import { getAssessmentReportResponse } from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { findQuestionById } from './Readiness-Assesment/ReadinessConfig';

interface Props {}

const AddReport: React.FC<Props> = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);

  // Store selected questions in an object format
  const [selectedQuestions, setSelectedQuestions] = useState<
    Record<string, number>
  >({});

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.READINESS_ASSESSMENT,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
    }
  }, []);

  const handleCheckboxChange = (questionId: string, questionAnswer: number) => {
    setSelectedQuestions((prevSelected) => {
      const updatedSelectedQuestions = { ...prevSelected };

      if (questionId in updatedSelectedQuestions) {
        delete updatedSelectedQuestions[questionId]; // Uncheck, so remove it from selectedQuestions
      } else {
        updatedSelectedQuestions[questionId] = questionAnswer; // Check, so add it to selectedQuestions
      }

      return updatedSelectedQuestions;
    });
  };

  return (
    <div
      className={cx(
        '',
        'h-[calc(100vh-9.6875rem)] bg-white px-10 pb-4 overflow-y-scroll rounded-xl'
      )}
    >
      <h1 className='text-2xl sticky top-0 bg-white pt-4 pb-6 z-30 font-semibold'>
        Readiness Assessment
      </h1>
      {Object.entries(response.assessment_response).map(
        ([questionId, questionAnswer], index) => (
          <div key={questionId} className='w-full relative bg-white mb-8'>
            <div className='flex space-x-6'>
              <input
                type='checkbox'
                className='h-5 w-5'
                onChange={() =>
                  handleCheckboxChange(questionId, Number(questionAnswer))
                }
                checked={questionId in selectedQuestions}
              />
              <div>
                Ques {index + 1}. {findQuestionById(questionId)}
              </div>
            </div>
            <div className='ml-11'>Ans. {questionAnswer as string}</div>
          </div>
        )
      )}
    </div>
  );
};

export default AddReport;
