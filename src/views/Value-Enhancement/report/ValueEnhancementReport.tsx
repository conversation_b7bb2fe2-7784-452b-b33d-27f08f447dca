import React, { useEffect, useState } from 'react';
import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import { FaDownload } from 'react-icons/fa';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import { EnhancementVEOs, idToQuestion } from '../ValueEnhancementConfig';
import TableValueReportComponent from './TableValueReportComponent';
import ValueEnhancementIntroduction from './ValueEnhancementIntroduction';
import DisclaimerPage from '../../Common/Disclaimer';

const ValueEnhancementReport = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);

  const dispatch = useDispatch();

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const enhancementResponse = Object.entries(
    response?.assessment_response
  ).reduce<{
    essential_veos: EnhancementVEOs[];
    supporting_veos: EnhancementVEOs[];
  }>(
    (acc, category: any) => {
      Object.entries(category[1])?.forEach(([veoKey, veoData]: any) => {
        if (
          idToQuestion[veoKey].desired_answers.includes(
            veoData?.selected_option
          )
        ) {
          if (veoKey.includes('(E)')) {
            acc.essential_veos.push({ veo: veoKey, pillar: category[0] });
          } else {
            acc.supporting_veos.push({ veo: veoKey, pillar: category[0] });
          }
        }
      });
      return acc;
    },
    { essential_veos: [], supporting_veos: [] }
  );

  const chunkArray = (array: EnhancementVEOs[], chunkSize: number) =>
    array.reduce<EnhancementVEOs[][]>((resultArray, item, index: number) => {
      const chunkIndex = Math.floor(index / chunkSize);

      if (!resultArray[chunkIndex]) {
        resultArray[chunkIndex] = []; // start a new chunk
      }

      resultArray[chunkIndex].push(item);

      return resultArray;
    }, []);

  const transformedVEOData = {
    essential_veos: chunkArray(enhancementResponse.essential_veos, 13),
    supporting_veos: chunkArray(enhancementResponse.supporting_veos, 13),
  };

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];

    transformedVEOData?.essential_veos?.forEach((value, index) => {
      reportPages.push(document.getElementById(`essential-veos-${index}`));
    });
    transformedVEOData?.supporting_veos?.forEach((value, index) => {
      reportPages.push(document.getElementById(`supporting-veos-${index}`));
    });
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `ValueEnhancementOpportunitiesReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => {
        setIsDownloading(false);
      },
      'portrait'
    );
  };
  const pageBreak = <div className='my-9' />;
  if (assessmentLoading) {
    return <Spinner customClassName='' spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
            disabled={isDownloading}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col h-[calc(100%-3.75rem)] overflow-y-auto pr-2 scrollbar'>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Value Enhancement Opportunities Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <ValueEnhancementIntroduction
              ownerName={response?.business_owner_data?.business_owner_name}
            />
          </LightThemeReportWrapper>
        </div>
        {transformedVEOData?.essential_veos?.map((value, index) => (
          <>
            {pageBreak}
            <div className='m-auto'>
              <LightThemeReportWrapper id={`essential-veos-${index}`}>
                <TableValueReportComponent
                  response={value}
                  sectionTitle='The Essential VEOs listed here require your attention'
                />
              </LightThemeReportWrapper>
            </div>
          </>
        ))}
        {transformedVEOData?.supporting_veos?.map((value, index) => (
          <>
            {pageBreak}
            <div className='m-auto'>
              <LightThemeReportWrapper id={`supporting-veos-${index}`}>
                <TableValueReportComponent
                  response={value}
                  sectionTitle='The Supporting VEOs listed here require your attention'
                />
              </LightThemeReportWrapper>
            </div>
          </>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default ValueEnhancementReport;
