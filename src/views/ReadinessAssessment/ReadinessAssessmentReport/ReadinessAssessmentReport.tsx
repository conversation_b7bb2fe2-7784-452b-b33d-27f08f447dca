import React, { useEffect, useState } from 'react';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { FaDownload } from 'react-icons/fa6';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Modal from 'shared-resources/components/Modal/Modal';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { getAssessmentReportResponse } from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import { getOrderedAwarenessAssessmentData } from 'utils/helpers/Helpers';
import {
  calculateCategoryPercentage,
  calculateOverallPercentage,
  getIndicatesText,
  getSubjectInfo,
} from 'views/Readiness-Assesment/readinessReportHelper';
import {
  BUSINESS_READINESS_QUESTIONS_DATA,
  FINANCE_QUESTIONS_DATA,
  PLANNING_READINESS_QUESTIONS_DATA,
  TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
  TRANSITION_OBJECTIVE_QUESTIONS_DATA,
} from 'views/contents/Readiness-Assessment/readinessAssessment.constant';
import { resetAssessmentReportData } from 'store/reducers/assessment-report.reducer';
import DarkThemeReportWrapper from '../../../HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from '../../../HOC/LightThemeReportWrapper';
import Button from '../../../shared-resources/components/Button/Button';
import { ReadinessAssessmentTitle } from '../../../utils/Readiness-Assessment/ReadinessAssessmentEnums';
import { generatePdf } from '../../../utils/generate-pdf/generatePdf-utils';
import CategoryResults from './CategoryResults';
import CoverPage from './CoverPage';
import ExplanationPage from './ExplanationPage';
import IntroductionPage from './IntrodunctionPage';
import OverallScorePage from './OverallScorePage';
import DisclaimerPage from '../../Common/Disclaimer';

const ReadinessAssessmentReport = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);

  useEffect(() => {
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      const orderedAwarenessData = getOrderedAwarenessAssessmentData(
        loggedInUserData as BusinessOwner
      );
      const canSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.READINESS_ASSESSMENT
      )?.toolData.is_report_sent_to_owner;

      if (!canSeeReport) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT_DASHBOARD}`
        );
      }
    }
  }, [loggedInUserData]);

  // cleanup function to reset the assessment report data
  useEffect(
    () => () => {
      dispatch(resetAssessmentReportData());
    },
    []
  );

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.READINESS_ASSESSMENT,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.READINESS_ASSESSMENT,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT_DASHBOARD}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const scores = [
    {
      name: ReadinessAssessmentTitle.BUSINESS_READINESS,
      score: calculateCategoryPercentage(
        response?.assessment_response,
        BUSINESS_READINESS_QUESTIONS_DATA
      ),
    },
    {
      name: ReadinessAssessmentTitle.FINANCE,
      score: calculateCategoryPercentage(
        response?.assessment_response,
        FINANCE_QUESTIONS_DATA
      ),
    },
    {
      name: ReadinessAssessmentTitle.TRANSITION_OBJECTIVES,
      score: calculateCategoryPercentage(
        response?.assessment_response,
        TRANSITION_OBJECTIVE_QUESTIONS_DATA
      ),
    },
    {
      name: ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE,
      score: calculateCategoryPercentage(
        response?.assessment_response,
        TRANSITION_KNOWLEDGE_QUESTIONS_DATA
      ),
    },
    {
      name: ReadinessAssessmentTitle.PLANNING,
      score: calculateCategoryPercentage(
        response?.assessment_response,
        PLANNING_READINESS_QUESTIONS_DATA
      ),
    },
  ];

  const overAllScore = calculateOverallPercentage(
    response?.assessment_response
  );

  const categoryResults = [
    {
      key: 1,
      categoryTitle: 'Score for Business Readiness',
      categoryScore: scores.find(
        (s) => s.name === ReadinessAssessmentTitle.BUSINESS_READINESS
      )?.score,
      categoryDescription:
        'The Business Readiness Score focuses on the preparedness of your business relative to management, owner reliance, business processes, customers and vendors.',
      categoryScoreDescription: (
        <p>
          Your score of{' '}
          <span className='font-semibold text-xl'>
            {
              scores.find(
                (s) => s.name === ReadinessAssessmentTitle.BUSINESS_READINESS
              )?.score
            }
            %
          </span>
          {getIndicatesText(
            ReadinessAssessmentTitle.BUSINESS_READINESS,
            scores.find(
              (s) => s.name === ReadinessAssessmentTitle.BUSINESS_READINESS
            )?.score
          )}
        </p>
      ),
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        BUSINESS_READINESS_QUESTIONS_DATA,
        1
      ),
    },
    {
      key: 2,
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        BUSINESS_READINESS_QUESTIONS_DATA,
        2
      ),
    },
    {
      key: 3,
      categoryTitle: 'Score For Finance',
      categoryScore: scores.find(
        (s) => s.name === ReadinessAssessmentTitle.FINANCE
      )?.score,
      categoryDescription:
        'The Finance score is indicative of your understanding and your preparedness in the areas of personal finance, business value and valuation, and business revenue and profitability. The components of the Finance section are foundational to the development of all exit/transition plans.',
      categoryScoreDescription: (
        <p>
          Your score of{' '}
          <span className='font-semibold text-xl'>
            {
              scores.find((s) => s.name === ReadinessAssessmentTitle.FINANCE)
                ?.score
            }
            %
          </span>
          {getIndicatesText(
            ReadinessAssessmentTitle.FINANCE,
            scores.find((s) => s.name === ReadinessAssessmentTitle.FINANCE)
              ?.score
          )}
        </p>
      ),
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        FINANCE_QUESTIONS_DATA,
        1
      ),
    },
    {
      key: 4,
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        FINANCE_QUESTIONS_DATA,
        2
      ),
    },
    {
      key: 5,
      categoryTitle: 'Score for Transition Objectives',
      categoryScore: scores.find(
        (s) => s.name === ReadinessAssessmentTitle.TRANSITION_OBJECTIVES
      )?.score,
      categoryDescription:
        'The Transition Objectives score is indicative of your understanding of the personal objectives that drive all transition planning activities, including what you want your post-transition life to look like.',
      categoryScoreDescription: (
        <p>
          Your score of{' '}
          <span className='font-semibold text-xl'>
            {
              scores.find(
                (s) => s.name === ReadinessAssessmentTitle.TRANSITION_OBJECTIVES
              )?.score
            }
            %
          </span>
          {getIndicatesText(
            ReadinessAssessmentTitle.TRANSITION_OBJECTIVES,
            scores.find(
              (s) => s.name === ReadinessAssessmentTitle.TRANSITION_OBJECTIVES
            )?.score
          )}
        </p>
      ),
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        TRANSITION_OBJECTIVE_QUESTIONS_DATA,
        1
      ),
    },
    {
      key: 6,
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        TRANSITION_OBJECTIVE_QUESTIONS_DATA,
        2
      ),
    },
    {
      key: 7,
      categoryTitle: 'Score for Transition Knowledge',
      categoryScore: scores.find(
        (s) => s.name === ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE
      )?.score,
      categoryDescription:
        'The Transition Knowledge score is indicative of your understanding of a variety of subjects not covered under other categories like preparation time, tax, buyer types, seller market conditions, and business value improvement.',
      categoryScoreDescription: (
        <p>
          Your score of{' '}
          <span className='font-semibold text-xl'>
            {
              scores.find(
                (s) => s.name === ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE
              )?.score
            }
            %
          </span>
          {getIndicatesText(
            ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE,
            scores.find(
              (s) => s.name === ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE
            )?.score
          )}
        </p>
      ),
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
        1
      ),
    },
    {
      key: 8,
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
        2
      ),
    },
    {
      key: 9,
      categoryTitle: 'Score for Planning',
      categoryScore: scores.find(
        (s) => s.name === ReadinessAssessmentTitle.PLANNING
      )?.score,
      categoryDescription:
        'The Planning score is indicative of your understanding of the need for planning focused on a group of transition topics not covered in other categories like company debt, deal structure, business and personal contingency planning, industry status, and building company value.',
      categoryScoreDescription: (
        <p>
          Your score of{' '}
          <span className='font-semibold text-xl'>
            {
              scores.find((s) => s.name === ReadinessAssessmentTitle.PLANNING)
                ?.score
            }
            %
          </span>
          {getIndicatesText(
            ReadinessAssessmentTitle.PLANNING,
            scores.find((s) => s.name === ReadinessAssessmentTitle.PLANNING)
              ?.score
          )}
        </p>
      ),
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        PLANNING_READINESS_QUESTIONS_DATA,
        1
      ),
    },
    {
      key: 10,
      subjectsInfo: getSubjectInfo(
        response?.assessment_response,
        PLANNING_READINESS_QUESTIONS_DATA,
        2
      ),
    },
  ];

  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
      document.getElementById(`overall-score-page`),
      document.getElementById(`explanation-page`),
    ];

    categoryResults.forEach((_, index) =>
      reportPages.push(document.getElementById(`category-result-${index}`))
    );
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `ReadinessReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => setIsDownloading(false),
      'portrait'
    );
  };

  const pageBreak = <div className='my-9' />;

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col h-[calc(100%-2.75rem)] overflow-y-auto pr-2 scrollbar '>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Readiness Assessment Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}{' '}
        <div className='h-[140rem] m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <IntroductionPage
              userName={response?.business_owner_data?.business_owner_name}
            />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='overall-score-page'>
            <OverallScorePage scores={scores} overAllScore={overAllScore} />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='explanation-page'>
            <ExplanationPage />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        {categoryResults.map((category, index) => (
          <>
            <div className='m-auto'>
              <LightThemeReportWrapper id={`category-result-${index}`}>
                <CategoryResults
                  categoryTitle={category.categoryTitle}
                  categoryDescription={category.categoryDescription}
                  categoryScoreDescription={category.categoryScoreDescription}
                  categoryScore={category.categoryScore}
                  subjectsInfo={category.subjectsInfo}
                />
              </LightThemeReportWrapper>
            </div>
            {pageBreak}
          </>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default ReadinessAssessmentReport;
