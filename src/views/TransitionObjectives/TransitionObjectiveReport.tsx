import React, { FC, memo, useEffect, useState } from 'react';
import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { FaDownload } from 'react-icons/fa';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import { BusinessGoals } from 'shared-resources/types/TransitionObjectives.type';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { getAssessmentReportResponse } from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import { getOrderedAwarenessAssessmentData } from 'utils/helpers/Helpers';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import { resetAssessmentReportData } from 'store/reducers/assessment-report.reducer';
import { getQuestions } from './Questions';
import QuestionsTableComp from './QuestionsTableComp';
import { getObjectives } from './TransitionObjectiveConfig';
import DisclaimerPage from '../Common/Disclaimer';

type TransitionObjectiveReportProps = {};
type QuestionAnswerTabProps = {
  question?: string;
  questionNumber: number;
  answer?: string | { key: string; value: string }[];
  options?: {
    value: string;
    id: number;
    textField?: boolean | undefined;
    question?: string;
  }[];
};
export const QuestionAnswerTab: FC<QuestionAnswerTabProps> = ({
  question,
  questionNumber,
  answer,
  options,
}) => {
  const secondaryQuestion =
    typeof answer === 'object' &&
    options?.find((option) => option?.value === answer?.[0]?.key)?.question;

  return (
    <div>
      <span className='font-medium !text-base'>{`${questionNumber}. ${question}?`}</span>
      <div className='rounded-lg  border-gray-300 border-2 py-2 px-6 font-montserrat text-blue-01 mt-2'>
        {Array.isArray(answer) ? (
          answer?.map((ans) => (
            <div
              key={ans?.key}
              className={`flex ${
                secondaryQuestion && 'flex-col'
              } gap-1 justify-start`}
            >
              <span className='!text-base' key={ans?.key}>
                {ans?.key}
              </span>
              {ans?.value && (
                <span className='!text-base'>
                  {' '}
                  {secondaryQuestion} - ({ans?.value})
                </span>
              )}
            </div>
          ))
        ) : (
          <span className='!text-base'>{answer}</span>
        )}
      </div>
    </div>
  );
};

const divideIntoSections = (assessmentToolResponse: {
  [key: number | string]:
    | string
    | BusinessGoals
    | { key: string; value: string }[]
    | any;
  orderedQuestionIds?: string[];
  lastID?: string;
}) => {
  const sections: {
    key: string;
    value: string | BusinessGoals | { key: string; value: string }[];
  }[][] = [];
  let currentSection: {
    key: string;
    value: string | BusinessGoals | { key: string; value: string }[];
  }[] = [];
  let currentItemIndex = 0;
  let firstItemIsObject = false;

  const { orderedQuestionIds } = assessmentToolResponse;
  if (orderedQuestionIds) {
    const newOrderedQuestionIds = [
      ...orderedQuestionIds,
      assessmentToolResponse.lastID,
    ] as string[];

    newOrderedQuestionIds?.forEach((key: string) => {
      const value = assessmentToolResponse[key];
      const isObject = typeof value === 'object' && !Array.isArray(value);

      if (!isObject) {
        currentSection.push({ key, value });
      }

      if (isObject) {
        if (currentSection.length === 0) {
          currentSection.push({ key, value });
          firstItemIsObject = true;
        } else if (firstItemIsObject && currentItemIndex < 4) {
          currentSection.push({ key, value });
        } else {
          sections.push([...currentSection]);
          currentSection = [{ key, value }];
          firstItemIsObject = true;
          currentItemIndex = 0;
        }
        currentItemIndex = 0;
      } else {
        currentItemIndex += 1;
        if (
          (firstItemIsObject && currentItemIndex === 4) ||
          currentItemIndex === 9
        ) {
          sections.push([...currentSection]);
          currentSection = [];
          currentItemIndex = 0;
          firstItemIsObject = false;
        }
      }
    });
  }
  if (currentSection.length > 0) {
    sections.push([...currentSection]);
  }

  return sections;
};
const TransitionObjectiveReport: FC<TransitionObjectiveReportProps> = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const dispatch = useDispatch();
  const [isCompleteOwner, setIsCompleteOwner] = useState(false);

  const [sections, setSections] = useState<
    {
      [key: number | string]: string | undefined;
    }[][]
  >([]);

  useEffect(() => {
    if (response?.assessment_response) {
      const s = divideIntoSections(response?.assessment_response);
      setSections(
        s as {
          [key: number | string]: string | undefined;
        }[][]
      );
    }
  }, [response?.assessment_response]);

  const getAnswerText = (answer: any) => {
    if (Array.isArray(answer)) {
      return answer;
    }
    if (typeof answer === 'object' && answer !== null) {
      return 'Object';
    }
    return answer;
  };

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];

    sections.forEach((_, index) =>
      reportPages.push(document.getElementById(`category-result-${index}`))
    );
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `TranstionReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => setIsDownloading(false),
      'portrait'
    );
  };
  const pageBreak = <div className='my-9' />;

  useEffect(() => {
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      const orderedAwarenessData = getOrderedAwarenessAssessmentData(
        loggedInUserData as BusinessOwner
      );
      const canSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.TRANSITION_OBJECTIVES
      )?.toolData.is_report_sent_to_owner;

      if (!canSeeReport) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES_DASHBOARD}`
        );
      }
    }
  }, [loggedInUserData]);

  let QuestionNumber = 0;
  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  // cleanup function to reset the assessment report data
  useEffect(
    () => () => {
      dispatch(resetAssessmentReportData());
    },
    []
  );

  useEffect(() => {
    if ((response?.assessment_response as any)?.owners) {
      const ownershipData = JSON.parse(
        (response?.assessment_response as any)?.owners
      ).find(
        (owner: { name: string; ownership: string }) =>
          owner.name === response?.business_owner_data?.business_owner_name
      );

      if (Number(ownershipData?.ownership) === 100) {
        setIsCompleteOwner(true);
      } else {
        setIsCompleteOwner(false);
      }
    }
  }, [response]);
  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col h-[calc(100%-3.75rem)] overflow-y-auto pr-2 scrollbar'>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Transition Objectives Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <div className='flex flex-col items-center h-full'>
              <div className='w-3/5 py-32'>
                <span className='text-blue-01 text-xl font-semibold'>
                  {`Congratulations ${response?.business_owner_data?.business_owner_name}`}
                </span>
                <p className='mt-12'>
                  You have taken an important step in identifying your future
                  life path. What we know about humans is that they can, and often
                  do change their minds. We also know that circumstances
                  change, sometimes by our choosing, sometimes not.
                  Changes to this document are not just acceptable but are encouraged,
                  as the circumstances of your life change. From time to time, your
                  advisor will remind you to review this document in order to be
                  sure that it always reflects your most current feelings about
                  your Transition objectives.
                </p>
              </div>
            </div>
          </LightThemeReportWrapper>
        </div>

        {sections.map((section, index) => (
          <>
            {pageBreak}
            <div className='m-auto'>
              <LightThemeReportWrapper id={`category-result-${index}`}>
                <div className='w-3/5 py-32 mx-auto h-full'>
                  <div className='flex flex-col gap-10'>
                    {getQuestions(isCompleteOwner).map((questionObj) => {
                      const sectionsObject: any = section.reduce(
                        (obj, sec) => ({
                          ...obj,
                          [sec.key ? sec.key.toString() : 'defaultKey']:
                            sec.value,
                        }),
                        {}
                      );
                      const answer = sectionsObject?.[questionObj.id];
                      if (answer) {
                        QuestionNumber += 1;
                      }

                      if (
                        answer &&
                        (typeof answer === 'string' || Array.isArray(answer))
                      ) {
                        return (
                          <QuestionAnswerTab
                            key={questionObj?.id}
                            questionNumber={QuestionNumber}
                            question={questionObj.question}
                            answer={getAnswerText(answer)}
                            options={questionObj.options}
                          />
                        );
                      }
                      if (
                        answer &&
                        typeof answer !== 'string' &&
                        !Array.isArray(answer)
                      )
                        return (
                          <div key={questionObj?.id}>
                            <span className='font-medium'>{`${QuestionNumber}. ${questionObj?.question}?`}</span>
                            <QuestionsTableComp
                              allowEditing={false}
                              headers={questionObj?.headers || ['']}
                              tableValues={answer as BusinessGoals}
                              objectives={getObjectives(questionObj)}
                              setTableValues={() => {}}
                              isReportView
                            />
                            {answer?.stakeHolderNotes?.notes && (
                              <div className='mt-2'>
                                <span className='font-medium'>Notes:</span>
                                <div className='rounded-lg  border-gray-300 border-2 py-2 px-6 font-montserrat text-blue-01 mt-2'>
                                  {answer?.stakeHolderNotes?.notes}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      return null;
                    })}
                  </div>
                </div>
              </LightThemeReportWrapper>
            </div>
          </>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};
export default memo(TransitionObjectiveReport);
