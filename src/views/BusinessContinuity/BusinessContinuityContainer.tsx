import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import {
  businessOwnerUpdate,
  fetchBusinessOwner,
} from 'store/actions/business-owner.action';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { useParamSelector } from 'store/selectors/base.selectors';
import {
  getBusinessOwnerDetails,
  getBusinessOwnerDetailsLoading,
} from 'store/selectors/business-owner.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentToolStatus,
  AssessmentTools,
  BusinessContinuityScreens,
  RouteKey,
  UserType,
} from 'types/enum';
import {
  getOrderedAwarenessAssessmentData,
  getUserRouteType,
  thankYouPageContent,
} from 'utils/helpers/Helpers';
import ThankYouPage from 'views/layout/ThankYouPage';
import { resetProgressStatus } from 'store/reducers/assessment-tool.reducer';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import {
  continuityNumberToScreenObject,
  continuityScreenToNumberObject,
  getHeaders,
} from './BusinessContinuityConfig';
import { getContinuityScreen } from './BusinessContinuityScreenConfig';
import BusinessContinuityHeader from './BusinessContinuityHeader';

const BusinessContinuityContainer = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const savedDraftData: any = useSelector(getAssessmentToolResponse);
  const progressStatus = useSelector(getAssessmentToolStatus);
  const businessOwnerProperties: any = useSelector(getBusinessOwnerProperties);
  const [businessContinuityData, setBusinessContinuityData] = useState<any>();
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id,
  }) as BusinessOwner;
  const [saveAsDraftClicked, setSaveAsDraftCliked] = useState(false);
  const businessOwnerData = businessOwner
    ? { ...businessOwnerProperties, email: businessOwner?.email }
    : businessOwnerProperties;

  const isLoading = useSelector(getAssessmentToolLoading);
  const isBusinessOwnerLoading = useSelector(getBusinessOwnerDetailsLoading);

  const [currentScreen, setCurrentScreen] = useState<{
    screen: BusinessContinuityScreens | undefined;
  }>();

  const backNextClickHandler = (nextScreen: BusinessContinuityScreens) => {
    setCurrentScreen({ screen: nextScreen });
  };
  const loggedInUser = useSelector(getUserData);
  const dispatch = useDispatch();

  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);

  useEffect(() => {
    if (
      progressStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetProgressStatus());
    }
  }, [assessmentReEvaluate, loggedInUser, progressStatus]);

  useEffect(() => {
    const screenNumber: number = savedDraftData?.saved_screen;

    if (
      progressStatus !== AssessmentToolProgressStatus.COMPLETED &&
      screenNumber &&
      !assessmentReEvaluate
    ) {
      if (screenNumber === 29 && loggedInUser?.type === UserType.ADVISOR) {
        setCurrentScreen({
          screen: continuityNumberToScreenObject[screenNumber - 1],
        });
      } else {
        setCurrentScreen({
          screen: continuityNumberToScreenObject[screenNumber],
        });
      }
    } else {
      setCurrentScreen({
        screen: BusinessContinuityScreens.GET_STARTED,
      });
    }
  }, [progressStatus]);

  useEffect(() => {
    const screen = new URLSearchParams(currentScreen as Record<string, string>);
    setSearchParams(screen);
  }, [currentScreen, setSearchParams, searchParams]);

  const updateBusinessOwner = (values: any) => {
    dispatch(
      businessOwnerUpdate(
        loggedInUser!,
        {
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          business_name: values.business_name,
          first_name: values.first_name,
          last_name: values.last_name,
          phone: values.phone,
          email: values?.email,
          street_address: values?.street_address,
          zip_code: values?.zip_code,
        },
        AssessmentTools.BUSINESS_CONTINUITY,
        +id!,
        setCurrentScreen
      )
    );
  };

  useEffect(() => {
    const orderedAwarenessData = getOrderedAwarenessAssessmentData(
      loggedInUser?.type === UserType.BUSINESS_OWNER
        ? (loggedInUser as BusinessOwner)
        : businessOwner
    );
    const isContinuityEnabled = !!orderedAwarenessData.find(
      (tool) =>
        tool?.toolData?.status === AssessmentToolStatus.ENABLE &&
        tool.toolName === AssessmentTools.BUSINESS_CONTINUITY
    );
    const handleFetchAssessmentError = () => {
      navigate(
        `/${getUserRouteType(loggedInUser?.type as UserType)}/${
          RouteKey.DASHBOARD
        }`
      );
    };
    // For Advisor Dashboard
    if (
      isContinuityEnabled &&
      businessOwner &&
      !isLoading &&
      businessOwner?.payment_details?.business_continuity
    ) {
      if (id && (loggedInUser?.type === UserType.ADVISOR || loggedInUser?.type === UserType.ENT_ADMIN)) {
        dispatch(
          fetchBusinessOwnerAssessment({
            tool: AssessmentTools.BUSINESS_CONTINUITY,
            businessOwnerId: +id!,
            onError: handleFetchAssessmentError,
          })
        );
      }
    } else if (
      businessOwner &&
      (!isLoading || !businessOwner?.payment_details?.business_continuity)
    ) {
      navigate(
        `${getUserRouteType(loggedInUser?.type as UserType)}/${
          RouteKey.DASHBOARD
        }`
      );
    }

    // For Business Owner Dashboard
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      if (
        (loggedInUser as BusinessOwner)?.payment_details?.business_continuity &&
        isContinuityEnabled
      ) {
        dispatch(
          fetchOwnAssessment({
            tool: AssessmentTools.BUSINESS_CONTINUITY,
            onError: handleFetchAssessmentError,
          })
        );
      } else {
        navigate(
          `${getUserRouteType(loggedInUser?.type as UserType)}/${
            RouteKey.DASHBOARD
          }`
        );
      }
    }
  }, [businessOwner]);

  useEffect(() => {
    if (!businessOwner && id && !isLoading && !isBusinessOwnerLoading) {
      dispatch(fetchBusinessOwner(id));
    }
  }, [id, businessOwner, isLoading, isBusinessOwnerLoading]);

  useEffect(() => {
    if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
      setBusinessContinuityData(savedDraftData);
    }
  }, [savedDraftData, progressStatus]);

  const handleFormSubmit = (saveAsDraft: boolean) => {
    let data = {
      ...businessContinuityData,
    };
    if (assessmentReEvaluate && currentScreen?.screen) {
      data = {
        ...businessContinuityData,
        saved_screen: continuityScreenToNumberObject[currentScreen.screen],
      };
    }

    if (loggedInUser?.type === UserType.BUSINESS_OWNER && !saveAsDraft) {
      data = {
        ...data,
        saved_screen:
          continuityScreenToNumberObject[
            BusinessContinuityScreens.SHOULD_PRIVATE
          ],
      };
    }

    if ((loggedInUser?.type === UserType.ADVISOR || loggedInUser?.type === UserType.ENT_ADMIN) && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          assessment_response: data,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          assessment_response: data,
          submit_type: saveAsDraft
          ? AssessmentResponseType.DRAFT
          : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            !saveAsDraft &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    if (!saveAsDraft) {
      setSaveAsDraftCliked(false);
    }
    setSubmitType(null);
  };

  useEffect(() => {
    if (submitType === AssessmentResponseType.DRAFT) {
      handleFormSubmit(true);
    } else if (submitType === AssessmentResponseType.COMPLETE) {
      handleFormSubmit(false);
    }
  }, [businessContinuityData]);

  if ((isLoading && !saveAsDraftClicked) || isBusinessOwnerLoading) {
    return <Spinner size='lg' />;
  }

  if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Business Continuity',
          loggedInUser?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={loggedInUser}
        isPasswordSet={false}
      />
    );
  }

  if (
    !isBusinessOwnerLoading &&
    (!isLoading || (isLoading && saveAsDraftClicked))
  )
    return (
      <div className='h-full flex flex-col gap-2'>
        <BusinessContinuityHeader
          subHeadings={getHeaders(
            currentScreen?.screen as BusinessContinuityScreens
          )}
        />
        <WithComments
          tool={AssessmentTools.BUSINESS_CONTINUITY}
          classname='h-full'
          containerClassname='h-full'
        >
          {getContinuityScreen(
            currentScreen!,
            loggedInUser!,
            businessContinuityData,
            setBusinessContinuityData,
            backNextClickHandler,
            setSubmitType,
            navigate,
            businessOwnerData,
            updateBusinessOwner,
            setSaveAsDraftCliked
          )}
        </WithComments>
      </div>
    );

  return null;
};

export default BusinessContinuityContainer;
