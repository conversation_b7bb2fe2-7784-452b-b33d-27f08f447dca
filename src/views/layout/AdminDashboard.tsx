import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import CreateOrEditAdvisorForm from 'shared-resources/components/Advisor/CreateEditAdvisor';
import {
  fetchAdvisorsList,
  updateAdvisorAccess,
} from 'store/actions/advisor.action';
import {
  allAdvisors,
  updateAdvisorLoading,
} from 'store/selectors/advisor.selector';
import { Advisor } from 'models/entities/Advisor';
import AdvisorAccessModal from 'shared-resources/components/Advisor/RevokeAdvisorAccount';
import { User } from 'models/entities/User';
import { RouteKey, UserType } from 'types/enum';
import CreateOrEditBusinessOwnerModal from 'shared-resources/components/BusinessOwner/Modals/CreateEditBo';
import CreateOrEditEnterpriseAdminModal from 'shared-resources/components/EnterpriseAdmin/CreateEditEnterpriseAdmin';
import EditProfileDialog from 'views/Profile/EditProfileDialog';

interface SearchFilters {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
  phone: string;
  companyName: string;
  businessOwnerCount: string;
  lastAccess: string;
  noOfAccess: string;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [modalVisible, setModalVisibility] = useState(false);
  const dispatch = useDispatch();
  const advisors = useSelector(allAdvisors);
  const [showDeleteAdvisorPopUp, setShowDeleteAdvisorPopUp] = useState(false);
  const [showCreateBOPopUp, setShowCreateBOPopUp] = useState(false);
  const [showCreateEAdminPopUp, setShowCreateEAdminPopUp] = useState(false);
  const deleteAdvisorLoading = useSelector(updateAdvisorLoading);
  const [advisorDetail, setAdvisorDetail] = useState<Advisor>();

  const revokeAccess = ['revoke', 'revoked', 'cannot', 'Active'];
  const grantAccess = ['grant', 'granted', 'can', 'Deactive'];

  const [searchFilters, setSearchFilters] = useState({
    id: '',
    firstName: '',
    lastName: '',
    email: '',
    status: '',
    phone: '',
    companyName: '',
    businessOwnerCount: '',
    lastAccess: '',
    noOfAccess: '',
  });

  const [showProfileDialog, setShowProfileDialog] = useState(false);

  const [userProfile, setUserProfile] = useState({} as User);

  useEffect(() => {
    dispatch(
      fetchAdvisorsList({
        filters: {},
      })
    ); // Fetch advisors on mount
  }, [dispatch]);

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setSearchFilters((prev: SearchFilters) => ({
      ...prev,
      [key]: value,
    }));
  };

  const transformUserProfile = (advisor: Advisor) => {
    const userData = {
      email: advisor.email,
      first_name: advisor.firstName,
      last_name: advisor.lastName,
      phone: advisor.phone,
      company_name: advisor.companyName,
      company_address: advisor.companyAddress,
      type: UserType.ADVISOR,
      id: advisor.id,
    } as User;
    setUserProfile(userData);
  };

  const filteredAdvisors = advisors.filter((advisor) => {
    if (Object.keys(advisor).length !== 0) {
      return (
        advisor.id.toString().includes(searchFilters.id) &&
        advisor.firstName
          .toLowerCase()
          .includes(searchFilters.firstName.toLowerCase()) &&
        advisor.lastName
          ?.toLowerCase()
          .includes(searchFilters.lastName.toLowerCase()) &&
        advisor.email
          .toLowerCase()
          .includes(searchFilters.email.toLowerCase()) &&
        (advisor.phone
          ? advisor.phone
              .toLowerCase()
              .includes(searchFilters.phone.toLowerCase())
          : !searchFilters.phone) &&
        (advisor.companyName
          ? advisor.companyName
              .toLowerCase()
              .includes(searchFilters.companyName.toLowerCase())
          : !searchFilters.companyName) &&
        (advisor.lastAccess && searchFilters.lastAccess
          ? advisor.lastAccess.includes(searchFilters.lastAccess)
          : !searchFilters.lastAccess) &&
        advisor.noOfAccess
          ?.toString()
          .includes(searchFilters.noOfAccess.toString()) &&
        String(advisor.businessOwnerCount).includes(
          searchFilters.businessOwnerCount
        ) &&
        (searchFilters.status === '' ||
          (advisor.isActive ? 'active' : 'inactive').includes(
            searchFilters.status.toLowerCase()
          ))
      );
    }
  });

  const handleAccessClick = (advisor: Advisor) => {
    const statusAction =
      advisor.isActive === undefined || advisor.isActive === false
        ? 'activate'
        : 'revoke';
    dispatch(
      updateAdvisorAccess({
        id:
          typeof advisor.id === 'string'
            ? parseInt(advisor.id, 10)
            : advisor.id,
        isActive: statusAction,
        onSuccess: () => {
          console.log('Page reloading after access change');
          navigate('/dashboard', { replace: true });
          navigate(0);
        },
      })
    );
  };

  const navigateToBOList = (advisorId: number) => {
    console.log('Navigating to BO List', advisorId);
    navigate('/admin/' + RouteKey.BO_LIST, {
      state: {
        isPrimaryAdvisor: true,
        advisorId,
      },
    });
    //navigate(0);
  };

  return (
    <div className='flex flex-col w-full h-full p-6'>
      {/* Top Actions */}
      <div className='flex justify-between items-center mb-4'>
        <h2 className='text-xl font-semibold'>Advisors List</h2>
        <div className='flex space-x-4'>
          <Button
            className='w-fit whitespace-nowrap py-2 px-6'
            theme='primary'
            onClick={() => setShowCreateEAdminPopUp(true)}
          >
            Create Enterprise Admin
          </Button>

          <Button
            className='w-fit whitespace-nowrap py-2 px-6'
            theme='primary'
            onClick={() => setModalVisibility(true)}
          >
            Create Advisor
          </Button>

          <Button
            className='w-fit whitespace-nowrap py-2 px-6'
            theme='primary'
            onClick={() => setShowCreateBOPopUp(true)}
          >
            Create Business Owner
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className='overflow-x-auto bg-white shadow-md rounded-lg'>
        <table className='min-w-full border border-gray-200'>
          <thead>
            <tr className='bg-gray-100 border-b'>
              <th className='py-2 px-4 text-left'>First Name</th>
              <th className='py-2 px-4 text-left'>Last Name</th>
              <th className='py-2 px-4 text-left'>Email</th>
              <th className='py-2 px-4 text-left'>Phone No</th>
              <th className='py-2 px-4 text-left'>Company Name</th>
              <th className='py-2 px-4 text-left'>Business Owners</th>
              <th className='py-2 px-4 text-center'>Actions</th>
              <th className='py-2 px-4 text-center'>Last Accessed</th>
              <th className='py-2 px-4 text-center'>No Of Access</th>
            </tr>
            <tr className='border-b'>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search First'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.firstName}
                  onChange={(e) =>
                    handleFilterChange('firstName', e.target.value)
                  }
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search Last'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.lastName}
                  onChange={(e) =>
                    handleFilterChange('lastName', e.target.value)
                  }
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search Email'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.email}
                  onChange={(e) => handleFilterChange('email', e.target.value)}
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search Phone'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.phone}
                  onChange={(e) => handleFilterChange('phone', e.target.value)}
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search Company Name'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.companyName}
                  onChange={(e) =>
                    handleFilterChange('companyName', e.target.value)
                  }
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search BO Count'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.businessOwnerCount}
                  onChange={(e) =>
                    handleFilterChange('businessOwnerCount', e.target.value)
                  }
                />
              </th>
              <th></th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search Last Accessed'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.lastAccess}
                  onChange={(e) =>
                    handleFilterChange('lastAccess', e.target.value)
                  }
                />
              </th>
              <th className='py-1 px-4'>
                <input
                  type='text'
                  placeholder='Search No Of Access'
                  className='w-full border px-2 py-1 rounded'
                  value={searchFilters.noOfAccess}
                  onChange={(e) =>
                    handleFilterChange('noOfAccess', e.target.value)
                  }
                />
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredAdvisors &&
              filteredAdvisors.map((advisor) => (
                <tr key={advisor.id} className='border-b'>
                  <td className='py-2 px-4'>
                    <button
                      className='text-blue-500 hover:underline'
                      onClick={() => {
                        transformUserProfile(advisor);
                        setShowProfileDialog(true);
                      }}
                    >
                      {advisor.firstName}
                    </button>
                  </td>
                  <td className='py-2 px-4'>{advisor.lastName}</td>
                  <td className='py-2 px-4'>{advisor.email}</td>
                  <td className='py-2 px-4'>{advisor.phone}</td>
                  <td className='py-2 px-4'>{advisor.companyName}</td>
                  <td className='py-2 px-4'>
                    <button
                      className='text-blue-500 hover:underline'
                      onClick={() => {
                        navigateToBOList(Number(advisor.id));
                      }}
                    >
                      {advisor.businessOwnerCount}
                    </button>
                  </td>

                  <td className='py-2 px-4 flex justify-center'>
                    <button
                      className={`relative w-14 h-6 flex items-center rounded-full p-1 transition-colors ${
                        advisor.isActive ? 'bg-green-500' : 'bg-red-500'
                      }`}
                      onClick={() => {
                        setAdvisorDetail(advisor);
                        setShowDeleteAdvisorPopUp(true);
                      }}
                    >
                      <span
                        className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                          advisor.isActive ? 'translate-x-7' : 'translate-x-0'
                        }`}
                      />
                    </button>
                  </td>
                  <td>
                    {advisor.lastAccess
                      ? new Date(advisor.lastAccess)
                          .toISOString()
                          .replace('T', ' ')
                          .split('.')[0]
                      : ''}
                  </td>

                  <td className='py-2 px-4'>{advisor.noOfAccess}</td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>

       {/* Create Enterprise Admin Modal */}
       {showCreateEAdminPopUp && (
        <Modal
          title='Create Enterprise Admin'
          visible={showCreateEAdminPopUp}
          handleVisibility={() => setShowCreateEAdminPopUp(false)}
          classname='w-[65.185rem]'
        >
          <CreateOrEditEnterpriseAdminModal
            handleModalVisibility={() => setShowCreateEAdminPopUp(false)}
            reloadEnterpriseAdmins={() => navigate('/dashboard')}
          />
        </Modal>
      )}

      {/* Create Advisor Modal */}
      {modalVisible && (
        <Modal
          title='Create Advisor'
          visible={modalVisible}
          handleVisibility={() => setModalVisibility(false)}
          classname='w-[65.185rem]'
        >
          <CreateOrEditAdvisorForm
            handleModalVisibility={() => setModalVisibility(false)}
            reloadAdvisors={() => navigate('/dashboard')}
          />
        </Modal>
      )}

      {showDeleteAdvisorPopUp && (
        <Modal
          title='Revoke Advisor'
          visible={showDeleteAdvisorPopUp}
          handleVisibility={setShowDeleteAdvisorPopUp}
        >
          <AdvisorAccessModal
            loading={deleteAdvisorLoading}
            onDeleteClick={() => {
              advisorDetail && handleAccessClick(advisorDetail);
            }}
            onCancelClick={() => setShowDeleteAdvisorPopUp(false)}
            message={advisorDetail?.isActive ? revokeAccess : grantAccess}
          />
        </Modal>
      )}

      {showCreateBOPopUp && (
        <Modal
          title='Create Business Owner'
          visible={showCreateBOPopUp}
          handleVisibility={() => setShowCreateBOPopUp(false)}
          classname='w-[65.185rem]'
        >
          <CreateOrEditBusinessOwnerModal
            handleModalVisibility={() => setShowCreateBOPopUp(false)}
            selectAdvisor={true}
          />
        </Modal>
      )}

      {showProfileDialog && (
        <Modal
          visible={showProfileDialog}
          title='Edit Profile'
          handleVisibility={setShowProfileDialog}
          classname='max-w-[46rem]'
        >
          <EditProfileDialog
            user={userProfile}
            updateByAdmin={true}
            onClose={() => {
              setShowProfileDialog(false);
              navigate('/dashboard');
              navigate(0);
            }}
          />
        </Modal>
      )}
    </div>
  );
};

export default AdminDashboard;
