/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
import React, { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import {
  resetAssessmentData,
  resetProgressStatus,
} from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
} from 'store/selectors/assessment-tool.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  FinancialGapAnalysisScreens,
  FinancialGapAssessmentCalculatorTabs,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import Spinner from '../../../shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from '../../../store/actions/assessment-tool.action';
import { getUserData } from '../../../store/selectors/user.selector';
import ThankYouPage from '../../layout/ThankYouPage';
import { screenToNumberObject as financialGapAnalysiscreenToNumberObject } from '../FinancialGapAnalysisConfig';
import {
  expenseCalculatorResponse,
  Expenses,
  formatCurrency,
  getTotalExpenses,
  handleBackTabSwitch,
  handleNextTabSwitch,
  numberToScreenObject,
  screenToNumberObject,
} from './ExpenseCalculatorConfig';
import TableComponent from './TableComponent';
import TotalExpense from './TotalExpense';

const ExpenseCalculator: React.FC = () => {
  const dispatch = useDispatch();
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const isLoading = useSelector(getAssessmentToolLoading);
  const progressStatus = useSelector(getAssessmentToolStatus);

  const [response, setResponse] = useState<Expenses>(expenseCalculatorResponse);
  const [currentTab, setCurrentTab] = useState<
    | {
        tab: FinancialGapAssessmentCalculatorTabs;
      }
    | undefined
  >();
  const loggedInUser = useSelector(getUserData);
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);

  useEffect(() => {
    const tab = new URLSearchParams(currentTab as Record<string, string>);
    setSearchParams(tab);
  }, [currentTab, setSearchParams, searchParams]);

  useEffect(() => {
    if (
      progressStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetProgressStatus());
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, loggedInUser, progressStatus]);

  // set saved tab
  useEffect(() => {
    if (!isLoading) {
      if (savedResponse?.expense_calculator_response?.saved_screen) {
        setCurrentTab({
          tab:
            numberToScreenObject[
              savedResponse?.expense_calculator_response?.saved_screen
            ] || FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
        });
      } else {
        setCurrentTab({
          tab: FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
        });
      }
    }
  }, [savedResponse, isLoading]);

  const handleSaveAsDraft = (
    finanancialGapAnalysisSavedScreen?: number,
    annual_expenses?: number,
    onSuccess?: () => void
  ) => {
    const postExitFinances = {
      ...savedResponse?.[FinancialGapAnalysisScreens.POST_EXIT_FINANCES],
    };

    if (annual_expenses !== undefined) {
      postExitFinances.annual_expenses = formatCurrency(annual_expenses)
        .toString()
        .replace('$', '');
    }
    let updatedResponse = {
      ...savedResponse,
      [FinancialGapAnalysisScreens.POST_EXIT_FINANCES]: postExitFinances,
      saved_screen:
        finanancialGapAnalysisSavedScreen || savedResponse?.saved_screen,
      expense_calculator_response: response,
    };

    if (currentTab?.tab) {
      updatedResponse = {
        ...updatedResponse,
        expense_calculator_response: {
          ...updatedResponse.expense_calculator_response,
          saved_screen: screenToNumberObject[currentTab.tab],
        },
      };
    }

    if ((loggedInUser?.type === UserType.ADVISOR || loggedInUser?.type === UserType.ENT_ADMIN) && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          assessment_response: updatedResponse,
          submit_type: AssessmentResponseType.DRAFT,
          onSuccess,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          assessment_response: updatedResponse,
          submit_type: AssessmentResponseType.DRAFT,
          onSuccess,
        })
      );
    }
  };

  const getFinancialGapScreenUrl = () => {
    if (id && loggedInUser?.type === UserType.ADVISOR) {
      return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/${AssessmentTools.FINANCIAL_GAP_ANALYSIS}`;
    }
    return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS}`;
  };

  const handleBackButton = () => {
    if (
      currentTab?.tab ===
      FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES
    ) {
      navigate(getFinancialGapScreenUrl());
    } else handleBackTabSwitch(currentTab, setCurrentTab);
  };

  const handleNextButton = () => {
    if (currentTab?.tab === FinancialGapAssessmentCalculatorTabs.EXPENSES) {
      handleSaveAsDraft(
        financialGapAnalysiscreenToNumberObject.post_exit_finances,
        getTotalExpenses(response) * 12,
        () => navigate(getFinancialGapScreenUrl())
      );
    } else handleNextTabSwitch(currentTab, setCurrentTab);
  };

  useEffect(() => {
    if (id && (loggedInUser?.type === UserType.ADVISOR || loggedInUser?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          businessOwnerId: +id!,
        })
      );
    }
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({ tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS })
      );
    }
  }, []);

  // set saved response
  useEffect(() => {
    if (savedResponse && savedResponse.expense_calculator_response) {
      setResponse(savedResponse.expense_calculator_response);
    }
  }, [savedResponse]);

  if (isLoading) {
    return <Spinner />;
  }

  if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent="You've completed the Wealth Gap Analysis. Thank you for taking time out of your busy day to get us your core details. Your advisor will be in touch to discuss results."
        loggedInUserData={loggedInUser}
        isPasswordSet={false}
      />
    );
  }

  return (
    <>
      <h1 className='font-bold text-2xl mb-3'>Wealth Gap Analysis</h1>
      <WithComments tool={AssessmentTools.FINANCIAL_GAP_ANALYSIS}>
        <div className='flex flex-col justify-between bg-white h-[calc(100vh-13rem)] px-4 py-5 w-full'>
          {currentTab &&
            (currentTab.tab ===
            FinancialGapAssessmentCalculatorTabs.EXPENSES ? (
              <TotalExpense totalMonthlyExpense={getTotalExpenses(response)} />
            ) : (
              <TableComponent
                currentTab={currentTab?.tab}
                response={response}
                setResponse={setResponse}
              />
            ))}
          <div className='pr-4 mt-2'>
            <BackNextComponent
              backStep={handleBackButton}
              nextStep={handleNextButton}
              buttonType='submit'
              isLoading={isLoading}
              isNextDisable={false}
              onSaveToDraftClick={handleSaveAsDraft}
            />
          </div>
        </div>
      </WithComments>
    </>
  );
};

export default ExpenseCalculator;
