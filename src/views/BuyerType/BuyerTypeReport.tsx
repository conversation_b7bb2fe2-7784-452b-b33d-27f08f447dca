import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import React, { useEffect, useState } from 'react';
import { FaDownload } from 'react-icons/fa';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import IntroductionPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/IntrodunctionPage';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import {
  BuyerReportPageArray,
  BuyerReportPageEnum,
  BuyerTypeScreens,
  convertObjectToFormattedResponse,
} from './BuyerTypeConfig';
import BuyerTypeTableComponent from './BuyerTypeTableComponent';
import NotIndentifiedTable from './BuyerTypeScreens/NotIndentifiedTable';
import DisclaimerPage from '../Common/Disclaimer';

const BuyerTypeReport: React.FC = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);

  const dispatch = useDispatch();

  useEffect(() => {
    if (id && (loggedInUserData?.type === UserType.ADVISOR || loggedInUserData?.type === UserType.ENT_ADMIN)) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const pageBreak = <div className='my-9' />;
  const res = response.assessment_response;

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];
    BuyerReportPageArray.forEach((_, index) => {
      reportPages.push(document.getElementById(`asset-page-${index}`));
    });
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `BuyerTypeReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => {
        setIsDownloading(false);
      },
      'portrait'
    );
  };

  const displayPage = (key: string) => {
    if (key === BuyerReportPageEnum.SCREEN_1) {
      return (
        <div key={key} className='px-10'>
          <BuyerTypeTableComponent
            response={convertObjectToFormattedResponse(
              (res as any)[BuyerTypeScreens.TYPE_OF_BUYER],
              ['Buyer Type', 'Value']
            )}
            heading='Buyer Type'
            text='How much do you know about the following types of buyers? Record your understanding of each using a scale of 1 to 6 where 1 indicates that you know almost nothing and 6 means you totally understand the buyer type.'
          />
          {Object.keys(res).includes(BuyerTypeScreens.ALREADY_IDENTIFIED) && (
            <div className='flex  flex-col  py-10 space-y-8 '>
              <h1>
                If you have identified a potential buyer, please name the buyer
              </h1>
              <div className='flex flex-col space-y-3'>
                {' '}
                <h1>Potential Buyer (if known)</h1>
                {(res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                  ?.potential_buyer ? (
                  <div
                    className={`border w-full rounded-lg px-8 py-4  font-medium  ${
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.potential_buyer
                        ? 'h-[5rem]'
                        : ''
                    }`}
                  >
                    {
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.potential_buyer
                    }
                  </div>
                ) : (
                  <div className='mt-4 text-lg font-medium'>No Data</div>
                )}
              </div>
              <div
                className={` ${
                  (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]?.buyer_type
                    ? 'flex justify-between items-center'
                    : ''
                }`}
              >
                {' '}
                <h1 className='w-[60%]'>
                  Which buyer type is the buyer you have identified?
                </h1>
                {(res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                  ?.buyer_type ? (
                  <div
                    className={`border rounded-lg px-8 py-4 font-medium w-[50%] `}
                  >
                    {(res as any)[
                      BuyerTypeScreens.ALREADY_IDENTIFIED
                    ]?.buyer_type
                      ?.replace(/_/g, ' ')
                      ?.replace(/\b\w/g, (char: string) => char.toUpperCase())}
                  </div>
                ) : (
                  <div className='mt-4 text-lg font-medium'>No Data</div>
                )}
              </div>
              <div className='flex flex-col space-y-3'>
                {' '}
                <h1>
                  Briefly explain any conversation you have had with this
                  potential buyer about buying your business.
                </h1>
                {(res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                  ?.conversation ? (
                  <div
                    className={`border w-full rounded-lg px-8 py-4  font-medium  ${
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.conversation
                        ? 'h-[5rem]'
                        : ''
                    }`}
                  >
                    {
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.conversation
                    }
                  </div>
                ) : (
                  <div className='mt-4 text-lg font-medium'>No Data</div>
                )}
              </div>
              <div className='flex flex-col space-y-3'>
                {' '}
                <h1>Briefly explain why you chose this buyer.</h1>
                {(res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                  ?.reason_for_choice ? (
                  <div
                    className={`border w-full rounded-lg px-8 py-4  font-medium  ${
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.reason_for_choice
                        ? 'h-[5rem]'
                        : ''
                    }`}
                  >
                    {
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.reason_for_choice
                    }
                  </div>
                ) : (
                  <div className='mt-4 text-lg font-medium'>No Data</div>
                )}
              </div>
              <div className='flex flex-col space-y-3'>
                {' '}
                <h1>Briefly describe the deal you envision with this buyer.</h1>
                {(res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                  ?.envisioned_deal ? (
                  <div
                    className={`border w-full rounded-lg px-8 py-4  font-medium  ${
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.envisioned_deal
                        ? 'h-[5rem]'
                        : ''
                    }`}
                  >
                    {
                      (res as any)[BuyerTypeScreens.ALREADY_IDENTIFIED]
                        ?.envisioned_deal
                    }
                  </div>
                ) : (
                  <div className='mt-4 text-lg font-medium'>No Data</div>
                )}
              </div>
            </div>
          )}
        </div>
      );
    }

    if (key === BuyerReportPageEnum.SCREEN_2) {
      return (
        <div className='mt-8' key={key}>
          <NotIndentifiedTable data={res} isReport />
          <div className='px-10 mt-6 flex flex-col space-y-5'>
            <h1 className='text-2xl font-medium'>
              Buyer Type (Not Identified)
            </h1>
            <div className='flex flex-col space-y-3'>
              <h1>Briefly describe your choices of buyer type:</h1>
              {(res as any)[BuyerTypeScreens.NOT_IDENTIFIED]
                ?.buyer_type_choice ? (
                <div className='border w-full rounded-lg px-8 py-4 font-medium h-[5rem]'>
                  {
                    (res as any)[BuyerTypeScreens.NOT_IDENTIFIED]
                      ?.buyer_type_choice
                  }
                </div>
              ) : (
                <div className='mt-4 text-lg font-medium'>No Data</div>
              )}
            </div>
            <div className='flex flex-col space-y-3'>
              <h1>
                Please briefly explain any deal details you might envision:
              </h1>
              {(res as any)[BuyerTypeScreens.NOT_IDENTIFIED]?.deal_details ? (
                <div className='border w-full rounded-lg px-8 py-4 font-medium h-[5rem]'>
                  {(res as any)[BuyerTypeScreens.NOT_IDENTIFIED]?.deal_details}
                </div>
              ) : (
                <div className='mt-4 text-lg font-medium'>No Data</div>
              )}
            </div>
          </div>
        </div>
      );
    }

    if (key === BuyerReportPageEnum.SCREEN_3) {
      return (
        <div key={key} className='px-10 flex  flex-col space-y-4'>
          {' '}
          <div>
            <BuyerTypeTableComponent
              isDealStructure
              response={convertObjectToFormattedResponse(
                (res as any)[BuyerTypeScreens.DEAL_STRUCTURE],
                ['Questions', 'Response'],
                true
              )}
              heading='Deal Structure'
              text='Record your understanding of the following questions/concepts using a scale of 1 to 6 where 1 indicates that you know almost nothing and 6 means you totally understand the difference.'
            />
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>
              Are you willing to stay engaged in the business for a period of
              time after the sale?
            </h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.engaged_after_sale ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.engaged_after_sale
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>
              Are there any pending legal issues that could affect the sale?
            </h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.pending_legal_issues ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.pending_legal_issues
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>
              Are there any reasons why you need to sell the business quickly?
            </h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.quick_sale_reason ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.quick_sale_reason
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>
              Are there any assets that you do not want to sell with the
              business?
            </h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.assets_not_for_sale ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.assets_not_for_sale
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>
              What is your expectation regarding any business liabilities at the
              time of sale?
            </h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.liabilities_at_sale ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.liabilities_at_sale
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>Are you willing to finance any part of the purchase price?</h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
              ?.willing_to_finance ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.willing_to_finance
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
          <div className='flex flex-col space-y-3'>
            {' '}
            <h1>Do you have a particular deal structure in mind?</h1>
            {(res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]?.deal_structure ? (
              <div
                className={`border w-full rounded-lg px-8 py-4  font-medium h-[5rem] `}
              >
                {
                  (res as any)[BuyerTypeScreens.DEAL_STRUCTURE_2]
                    ?.deal_structure
                }
              </div>
            ) : (
              <div className='mt-4 text-lg font-medium'>No Data</div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  if (assessmentLoading) {
    return <Spinner customClassName='' spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
            disabled={isDownloading}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col h-[calc(100%-3.75rem)] overflow-y-auto pr-2 scrollbar'>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Buyer Type-Deal Structure Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone ?? ''}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <IntroductionPage
              userName={response?.business_owner_data?.business_owner_name}
              pageContent={
                <div>
                  The purpose of the Buyer Type-Deal Structure tool is to get
                  you to begin thinking about both whom you want to transition
                  the business and what the deal might look like. The intent is
                  not to make you expert in any of the topics, but rather for
                  you to begin to understand what you need to know and how it
                  impacts your transition planning. This Report contains your
                  responses to all the questions asked of you in the Buyer
                  Type-Deal Structure questionnaire. Some of the topics you had
                  prior knowledge of and others you did not. All of your answers
                  are included because it is likely that you will need to deepen
                  your understanding of topics you are familiar with and begin
                  to understand others.
                </div>
              }
              classname='pt-64 px-12'
            />
          </LightThemeReportWrapper>
        </div>
        {BuyerReportPageArray.filter((key) =>
          Object.keys((res as any)?.[BuyerTypeScreens.ALREADY_IDENTIFIED] || {})
            ?.length > 0
            ? key !== BuyerReportPageEnum.SCREEN_2
            : true
        ).map((key, index) => (
          <>
            {pageBreak}
            <div className='m-auto'>
              <LightThemeReportWrapper id={`asset-page-${index}`}>
                <div className='p-32'>{displayPage(key) as any}</div>
              </LightThemeReportWrapper>
            </div>
          </>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>

      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default BuyerTypeReport;
