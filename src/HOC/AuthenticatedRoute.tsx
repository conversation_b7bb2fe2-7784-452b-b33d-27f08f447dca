import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
// services
import { localStorageService } from 'services/LocalStorageService';
// actions
import { authFetchMeAction } from 'store/actions/auth.action';
// selectors
import {
  isAuthenticatedSelector,
  isAuthLoadingSelector,
} from 'store/selectors/auth.selector';
// components
import Spinner from 'shared-resources/components/Spinner/Spinner';
// utils
import {
  getAdvisorSubscriptionData,
  getBackupCodes,
  getUserData,
} from 'store/selectors/user.selector';
import {
  RouteKey,
  SubscriptionStatus,
  UserRouteType,
  UserType,
} from 'types/enum';
import { getDefaultRoute, getUserRouteType } from 'utils/helpers/Helpers';
import SubscriptionPage from 'views/AdvisorSubscription/SubscriptionPage';
import Modal from 'shared-resources/components/Modal/Modal';
import BackupCodesModal from 'shared-resources/components/Modals/BackupCodesModal';
import { SetBackupCodes } from 'store/reducers/user.reducer';

const AuthenticatedRouteHOC = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  const AuthenticatedRoute: React.FC<P> = ({ ...props }) => {
    const dispatch = useDispatch();
    const location = useLocation();
    const backupCodes = useSelector(getBackupCodes);
    const [showBackupCodesModal, setShowBackupCodesModal] = useState(false);

    const advisorSubscriptionInfo: any = useSelector(
      getAdvisorSubscriptionData
    );

    const isAuthenticated = useSelector(isAuthenticatedSelector);
    const isLoading = useSelector(isAuthLoadingSelector);

    const loggedInUserType = useMemo(
      () => localStorageService.getLoggedInUserType(),
      []
    );

    useEffect(() => {
      if (backupCodes.length > 0) {
        setShowBackupCodesModal(true);
      } else {
        setShowBackupCodesModal(false);
      }
    }, [backupCodes]);

    const isRouteForUser =
      location.pathname.split('/')[1] === getUserRouteType(loggedInUserType) ||
      (location.pathname.split('/')[1] === 'advisor' &&
        !location.pathname.includes('/advisor/dashboard') &&
        getUserRouteType(loggedInUserType) === 'enterprise_admin');

    const path = location.pathname.split('/')[1] ?? UserType.ADVISOR;
    const navigate = useNavigate();
    const token = localStorageService.getAuthToken();

    useEffect(() => {
      if (token && !isAuthenticated && !isLoading) {
        dispatch(
          authFetchMeAction({
            userType: path.length
              ? (path as UserRouteType)
              : getUserRouteType(loggedInUserType),
          })
        );
      } else if (!token) {
        navigate(`/${getUserRouteType(loggedInUserType)}/login`);
      }
    }, [dispatch, isAuthenticated, isLoading, location.pathname]);

    if (isLoading) {
      return (
        <div className='flex'>
          <Spinner size='lg' />
        </div>
      );
    }

    if (isAuthenticated || token) {
      console.log('expiry date', advisorSubscriptionInfo.expire_at);
      if (
        isRouteForUser &&
        loggedInUserType === UserType.ADVISOR &&
        advisorSubscriptionInfo?.primaryAdvisor &&
        (advisorSubscriptionInfo.status === SubscriptionStatus.INACTIVE ||
          (advisorSubscriptionInfo.trialUser &&
            advisorSubscriptionInfo.expire_at < new Date().toISOString()))
      ) {
        // Redirecting to subscription page if not subscribed
        return (
          <>
            <Navigate
              to={`/${UserRouteType.ADVISOR}/${RouteKey.SUBSCRIPTION}`}
            />
            <SubscriptionPage />
          </>
        );
      }

      if (showBackupCodesModal) {
        return (
          <Modal
            visible={showBackupCodesModal}
            title=''
            handleVisibility={() => {}} // Prevent closing by clicking outside
            closeOnOutsideClick={false}
            classname='max-w-lg'
          >
            <BackupCodesModal
              onClose={() => {
                SetBackupCodes({ codes: [] });
                navigate(0);
              }}
              onCopied={() => {
                SetBackupCodes({ codes: [] });
                navigate(0);
              }}
            />
          </Modal>
        );
      } else {
        if (isRouteForUser) {
          return <Component {...(props as P)} />;
        }
        return (
          <Navigate to={getDefaultRoute(loggedInUserType)} />
        );
      }
    }

    return (
      <Navigate
        to={`/${getUserRouteType(loggedInUserType) ?? 'advisor'}/login`}
      />
    );
  };

  return AuthenticatedRoute;
};

export default AuthenticatedRouteHOC;
