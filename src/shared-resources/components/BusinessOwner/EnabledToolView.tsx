import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useEffect, useState } from 'react';
import { MdOutlineArrowOutward } from 'react-icons/md';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { Link } from 'react-router-dom';
import { sendAssessmentReport } from 'store/actions/assessment-report.action';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateAssessmentReEvaluate,
} from 'store/actions/assessment-tool.action';
import { getAssessmentReportLoading } from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentToolProgressStatus,
  AssessmentTools,
  NonAssessmentToolTabs,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import { deleteDocument } from 'store/actions/document.action';
import { documentsLoading } from 'store/selectors/document.selector';
import {
  getReportType,
  getSingularPlural,
} from '../../../utils/helpers/Helpers';
import { handleDownloadAsCSV } from '../../../views/BusinessValuation/BusinessValuationConfig';
import Button from '../Button/Button';
import Modal from '../Modal/Modal';
import FollowUpHistoryModal from './Modals/Follow Up/FollowUpHistoryModal';
import FollowUpModal from './Modals/Follow Up/FollowUpModal';
import FollowUpSuccessModal from './Modals/Follow Up/FollowUpSuccessModal';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getLastFollowUpDiff,
  getOpenCount,
  getReevaluate,
} from '../../../store/selectors/assessment-tool.selector';
import DocumentsModal from './Modals/DocumentModal';
import DeleteDocumentModal from './Modals/Follow Up/DeleteDocumentModal';

interface EnabledToolViewProps {
  businessOwner: BusinessOwner;
  tool: AssessmentTools | NonAssessmentToolTabs;
  showDownload: boolean;
}

const EnabledToolView: React.FC<EnabledToolViewProps> = (props) => {
  const { businessOwner, tool, showDownload } = props;
  const navigate = useNavigate();
  const { id } = useParams();
  const dispatch = useDispatch();
  const userData = useSelector(getUserData);
  const sendReportLoading = useSelector(getAssessmentReportLoading);
  const assessmentData = useSelector(getAssessmentToolResponse);
  const loading = useSelector(documentsLoading);
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const assessmentLoading = useSelector(getAssessmentToolLoading);
  const openCount = useSelector(getOpenCount);
  const reevaluate = useSelector(getReevaluate);
  const lastFollowUpDiff = useSelector(getLastFollowUpDiff);
  const [showDeleteDocumentPopUp, setShowDeleteDocumentPopUp] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);

  const [followUpModalVisible, setFollowUpModalVisibility] =
    useState<boolean>(false);
  const [documentModalVisible, setDocumentModalVisible] =
    useState<boolean>(false);

  const [followUpHistoryModalVisible, setFollowUpHistoryModalVisibility] =
    useState<boolean>(false);

  const [followUpSuccessModalVisible, setFollowUpSuccessModalVisible] =
    useState<boolean>(false);

  const handleFollowUpModalVisibility = (value: boolean) => {
    setFollowUpModalVisibility(value);
    if (followUpHistoryModalVisible) setFollowUpHistoryModalVisibility(false);
  };

  const handleFollowUpHistoryModalVisibility = (value: boolean) => {
    setFollowUpHistoryModalVisibility(value);
  };

  const handleFollowUpSuccessModalVisibility = (value: boolean) => {
    setFollowUpSuccessModalVisible(value);
  };

  const handleDocumentModalVisibility = (value: boolean) => {
    setDocumentModalVisible(value);
  };
  useEffect(() => {
    if (
      (userData?.type === UserType.ADVISOR ||
        userData?.type === UserType.ENT_ADMIN) &&
      businessOwner.id
    ) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: tool as AssessmentTools,
          businessOwnerId: +businessOwner.id,
        })
      );
    } else if (userData?.type === UserType.BUSINESS_OWNER) {
      dispatch(fetchOwnAssessment({ tool: tool as AssessmentTools }));
    }
  }, [tool]);

  const handleOpenAssessment = () => {
    navigate(
      `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${tool}`
    );
  };

  const handleAssessmentReEvaluate = () => {
    if (businessOwner) {
      dispatch(
        updateAssessmentReEvaluate({
          ownerId: +businessOwner.id,
          status: true,
          tool,
        })
      );
    }
  };

  const isPrimaryAdvisor = businessOwner?.primary_advisor_id === userData?.id;
  const getTitle = () => {
    if (tool !== AssessmentTools.BUSINESS_VALUATION) {
      return 'Open Assessment';
    }

    if (businessOwner?.payment_details?.business_valuation) {
      return 'Open Business Valuation';
    }

    return 'Pay for Business Valuation';
  };
  const isDocumentAvailable = () => {
    if (
      tool === AssessmentTools.BUSINESS_VALUATION ||
      tool === AssessmentTools.ASSET_PROTECTION ||
      tool === NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS
    ) {
      return true;
    }
    return false;
  };
  const allStageAssessmentData = {
    ...businessOwner?.assessment_data?.awareness,
    ...businessOwner?.assessment_data?.plan_development,
  };
  const isCurrentAssessmentCompleted =
    allStageAssessmentData?.[tool]?.progress_status ===
    AssessmentToolProgressStatus.COMPLETED;

  const isReportSent = allStageAssessmentData?.[tool]?.is_report_sent_to_owner;

  const getReportUrl = () => {
    switch (tool) {
      case AssessmentTools.READINESS_ASSESSMENT:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.READINESS_REPORT}`;
      case AssessmentTools.OWNER_RELIANCE:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.OWNER_RELIANCE_REPORT}`;
      case AssessmentTools.TRANSITION_OBJECTIVES:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.TRANSITION_REPORT}`;
      case AssessmentTools.BUSINESS_CONTINUITY:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.BUSINESS_CONTINUITY_REPORT}`;
      case AssessmentTools.FINANCIAL_GAP_ANALYSIS:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.FINANCIAL_GAP_REPORT}`;
      case AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_REPORT}`;
      case AssessmentTools.ASSET_PROTECTION:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.ASSET_PROTECTION_REPORT}`;
      case AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE:
        return `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_REPORT}`;
      default:
        return ``;
    }
  };

  const showReEvaluateButton =
    isCurrentAssessmentCompleted && !assessmentReEvaluate && !assessmentLoading;
  const handleDelete = (item: any) => {
    dispatch(
      deleteDocument({
        id: id!,
        type: getReportType(tool as AssessmentTools | NonAssessmentToolTabs),
        reportId: item.id,
        onSuccess: () => setShowDeleteDocumentPopUp(false),
      })
    );
  };

  return (
    <div className='flex flex-col mt-5'>
      <div className='flex justify-between'>
        <div className='flex gap-4'>
          <Button className='px-6 py-2' onClick={handleOpenAssessment}>
            {getTitle()}
          </Button>
          {showReEvaluateButton && (
            <Button
              theme='tertiary'
              className='px-6 py-2'
              onClick={handleAssessmentReEvaluate}
            >
              Re-Evaluate
            </Button>
          )}
          {isDocumentAvailable() && (
            <Button
              className='px-6 py-2'
              theme='tertiary'
              type='button'
              onClick={() => handleDocumentModalVisibility(true)}
            >
              Documents
            </Button>
          )}
        </div>
        <div className='flex gap-16'>
          <div className='flex text-blue-01 gap-2'>
            <button
              onClick={() => {
                handleFollowUpHistoryModalVisibility(true);
              }}
              className='flex text-blue-01 w-fit gap-2'
            >
              <span className='whitespace-nowrap'>
                View Full Follow up History
              </span>
              <MdOutlineArrowOutward size={24} />
            </button>
          </div>
          {isCurrentAssessmentCompleted &&
            tool !== AssessmentTools.BUSINESS_VALUATION &&
            tool !== AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING && (
              <Link to={getReportUrl()}>
                <div className='flex text-blue-01 w-fit gap-2'>
                  <span className='whitespace-nowrap'>View Report</span>
                  <MdOutlineArrowOutward size={24} />
                </div>
              </Link>
            )}
        </div>
      </div>
      <div className='flex mt-4 justify-between items-center'>
        <div className='flex gap-10 1.5xl:gap-40'>
          <div className='flex flex-col'>
            <span className='font-medium'>Opens</span>
            <span className='text-blue-01 mt-2'>{openCount}</span>
          </div>
          <div className='flex flex-col'>
            <span className='font-medium'>Last Follow Up</span>
            <span className='text-blue-01 mt-2'>
              {lastFollowUpDiff !== null
                ? `${lastFollowUpDiff} day${getSingularPlural(
                    lastFollowUpDiff
                  )} ago`
                : '-'}
            </span>
          </div>
        </div>
        <div className='flex gap-3 w-fit'>
          {showDownload && !assessmentLoading && (
            <Button
              onClick={() => handleDownloadAsCSV(assessmentData)}
              className='px-6 py-2'
              theme='tertiary'
            >
              Download
            </Button>
          )}
          {isPrimaryAdvisor && (
            <Button
              onClick={() => {
                handleFollowUpModalVisibility(true);
              }}
              theme='tertiary'
              className='px-6 py-2'
            >
              Quick Follow Up
            </Button>
          )}
          {!isReportSent &&
            !reevaluate &&
            isCurrentAssessmentCompleted &&
            tool !== AssessmentTools.BUSINESS_VALUATION &&
            tool !== AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING && (
              <Button
                isSubmitting={sendReportLoading}
                onClick={() => {
                  dispatch(
                    sendAssessmentReport({
                      businessOwnerId: +businessOwner.id,
                      assessmentTool: tool as AssessmentTools,
                    })
                  );
                }}
                theme='tertiary'
                className='px-6 py-2'
              >
                Send Report To Business Owner Dashboard
              </Button>
            )}
        </div>
      </div>
      {followUpModalVisible && (
        <Modal
          title='Quick Follow Up'
          visible={followUpModalVisible}
          handleVisibility={handleFollowUpModalVisibility}
          classname='w-[48rem]'
          closeOnOutsideClick
        >
          <FollowUpModal
            handleModalVisibility={handleFollowUpModalVisibility}
            handleFollowUpSuccessVisibility={
              handleFollowUpSuccessModalVisibility
            }
            businessOwner={businessOwner}
            tool={tool}
          />
        </Modal>
      )}
      {followUpHistoryModalVisible && (
        <Modal
          title='Full Follow Up Details'
          visible={followUpHistoryModalVisible}
          handleVisibility={handleFollowUpHistoryModalVisibility}
          classname='w-[60rem]'
          closeOnOutsideClick
        >
          <FollowUpHistoryModal
            businessOwner={businessOwner}
            tool={tool}
            handleModalVisibility={handleFollowUpModalVisibility}
          />
        </Modal>
      )}
      {followUpSuccessModalVisible && (
        <Modal
          visible={followUpSuccessModalVisible}
          handleVisibility={handleFollowUpSuccessModalVisibility}
          classname='w-[40rem]'
          closeOnOutsideClick
        >
          <FollowUpSuccessModal
            handleModalVisibility={handleFollowUpSuccessModalVisibility}
          />
        </Modal>
      )}
      {documentModalVisible && (
        <Modal
          title='Documents'
          visible={documentModalVisible}
          handleVisibility={handleDocumentModalVisibility}
          classname='w-[66%]'
        >
          <DocumentsModal
            onCancelClick={handleDocumentModalVisibility}
            setSelectedDocument={setSelectedDocument}
            setShowDeleteDocumentPopUp={setShowDeleteDocumentPopUp}
            tool={tool}
          />
        </Modal>
      )}
      {showDeleteDocumentPopUp && (
        <Modal
          title='Delete Document'
          visible={showDeleteDocumentPopUp}
          handleVisibility={setShowDeleteDocumentPopUp}
        >
          <DeleteDocumentModal
            loading={loading}
            onDeleteClick={() => handleDelete(selectedDocument)}
            onCancelClick={() => setShowDeleteDocumentPopUp(false)}
          />
        </Modal>
      )}
    </div>
  );
};
export default EnabledToolView;
