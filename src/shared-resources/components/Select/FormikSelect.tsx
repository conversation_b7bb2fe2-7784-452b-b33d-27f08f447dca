import { Listbox } from '@headlessui/react';
import { useField } from 'formik';
import React, { useMemo, useState } from 'react';
import { FaCaretDown } from 'react-icons/fa';
import InputHelper from '../Inputhelper/InputHelper';

export interface FormikSelectOption {
  label: string;
  value: string;
}
interface FormikSelectProps {
  name: string;
  label: string;
  options: FormikSelectOption[];
  placeholder?: string;
  labelClassName?: string;
  asterisk?: boolean;
  outerClassName?: string;
  menuPlacement?: 'top' | 'bottom';
  menuClassname?: string;
  optionClassname?: string;
  classname?: string;
  errorClassname?: string;
  isSearch?: boolean;
}

export const FormikSelect: React.FC<FormikSelectProps> = (props) => {
  const {
    name,
    label,
    options,
    placeholder,
    labelClassName,
    outerClassName,
    menuPlacement,
    asterisk,
    menuClassname,
    optionClassname,
    classname,
    errorClassname,
    isSearch = false, // <-- default is false
  } = props;

  const [field, meta, helpers] = useField({ name });
  const [search, setSearch] = useState('');

  const filteredOptions = useMemo(() => {
    return isSearch
      ? options.filter((opt) =>
          opt.label.toLowerCase().includes(search.toLowerCase())
        )
      : options;
  }, [options, search, isSearch]);

  return (
    <>
      {filteredOptions.length > 4 && (
        <style>
          {`
            .formik-select-scrollable {
              overflow-y: scroll !important;
              scrollbar-width: thin !important;
              scrollbar-color: #93C5FD #E1F0F6 !important;
            }
            .formik-select-scrollable::-webkit-scrollbar {
              width: 14px !important;
              display: block !important;
              background: #E1F0F6 !important;
            }
            .formik-select-scrollable::-webkit-scrollbar-track {
              background: #E1F0F6 !important;
              border-radius: 7px !important;
              display: block !important;
            }
            .formik-select-scrollable::-webkit-scrollbar-thumb {
              background: #93C5FD !important;
              border-radius: 7px !important;
              min-height: 30px !important;
              display: block !important;
              border: 2px solid #E1F0F6 !important;
            }
            .formik-select-scrollable::-webkit-scrollbar-thumb:hover {
              background: #20A0D6 !important;
            }
            .formik-select-scrollable::-webkit-scrollbar-corner {
              background: #E1F0F6 !important;
            }
          `}
        </style>
      )}
      <Listbox
        value={field.value}
        onChange={(value: string) => {
          field.onChange({ target: { value, name } });
        }}
      >
      <div
        className={`relative ${classname}`}
        onBlur={() => {
          helpers.setTouched(true);
        }}
      >
        <Listbox.Label>
          <span
            className={`text-xl font-medium mb-1 block leading-6.5 ${labelClassName}`}
          >
            {label}
            {asterisk && <span className='text-red-500 ml-1'>*</span>}
          </span>
        </Listbox.Label>
        <Listbox.Button
          className={`px-4 ${outerClassName} relative w-full h-12 rounded-xl border border-gray-03 bg-white text-black`}
        >
          <span className='flex items-center text-black-02 truncate font-medium justify-start'>
            {options.find((option) => option.value === field.value)?.label ??
              placeholder}
          </span>
          <div className='absolute inset-y-0  right-0 ml-2 bg-white flex items-center mr-4 pointer-events-none'>
            <FaCaretDown size={20} className='text-black-02 text-base' />
          </div>
        </Listbox.Button>
        <Listbox.Options
          className={`absolute ${
            menuPlacement === 'top' ? 'bottom-full -mb-6' : 'mt-1'
          } ${menuClassname} z-20 border border-gray-03 w-full rounded-xl bg-white focus:outline-none ${
            filteredOptions.length > 4 ? 'formik-select-scrollable' : 'overflow-auto'
          }`}
          style={filteredOptions.length > 4 ? {
            maxHeight: isSearch ? '320px' : '280px', // Increased height, more for search
            overflowY: 'scroll',
            scrollbarWidth: 'thin',
            scrollbarColor: '#93C5FD #E1F0F6'
          } : { overflow: 'auto' }}
        >
          <div className="pt-3 pb-4 px-2.5">
            {isSearch && (
              <input
                type='text'
                placeholder='Search...'
                className='w-full mb-2 px-3 py-2 border border-gray-300 rounded'
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            )}
            {filteredOptions.length > 0 ? (
              <>
                {filteredOptions.map((option) => (
                  <Listbox.Option
                    key={option.value}
                    className={`relative ${optionClassname} cursor-default rounded-xl hover:cursor-pointer hover:bg-blue-02 mt-1 py-4 px-2.5 text-black-02 ${
                      option.value === field?.value && 'bg-blue-02'
                    }`}
                    value={option.value}
                  >
                    <span className='font-medium'>{option.label}</span>
                  </Listbox.Option>
                ))}
                {/* Force scrollbar to appear by adding extra height */}
                {filteredOptions.length > 4 && (
                  <div style={{ height: '10px', width: '100%' }} aria-hidden="true"></div>
                )}
              </>
            ) : (
              <div className='px-2 py-4 text-gray-400 text-center'>
                No options found
              </div>
            )}
          </div>
        </Listbox.Options>
        <div className={`h-5 mt-1 ${errorClassname}`}>
          {meta.error && meta.touched && (
            <InputHelper type='error' text={meta.error} />
          )}
        </div>
      </div>
    </Listbox>
    </>
  );
};
