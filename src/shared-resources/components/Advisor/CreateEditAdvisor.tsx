import { Formik, Form } from 'formik';
import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { Advisor } from 'models/entities/Advisor';
import { AdvisorDTO } from 'dtos/advisor.dto';
import {
  getCreateAdvisorErrors,
  getCreateAdvisorLoading,
  getUpdateAdvisorLoading,
} from 'store/selectors/advisor.selector';
import { advisorCreate, updateAdvisor } from 'store/actions/advisor.action';
import {
  fetchEnterpriseById,
  fetchEnterprisesList,
} from 'store/actions/enterprise.action';
import { allEnterprises, getEnterpriseById } from 'store/selectors/enterprise.selector';
import { useParamSelector } from 'store/selectors/base.selectors';
import FormikCheckbox from '../CheckBox/FormikCheckbox';
import { FormikSelect } from '../Select/FormikSelect';

interface CreateOrEditAdvisorForm {
  first_name: string;
  last_name?: string;
  email: string;
  phone: string;
  company_name: string;
  company_address?: string;
  is_primary_advisor?: boolean;
  trial?: boolean;
  trial_duration?: string;

  last_access?: number;
  no_of_access?: number;
  ep_id?: number;
  is_ep_user?: boolean;
}

interface CreateOrEditAdvisorModalProps {
  handleModalVisibility: (value: boolean) => void;
  advisor?: Advisor;
  onRevokeAdvisorClick?: () => void;
  reloadAdvisors: () => void;
  ep_id?: number;
}

const CreateOrEditAdvisorModal: React.FC<CreateOrEditAdvisorModalProps> = (
  props
) => {
  const {
    handleModalVisibility,
    advisor,
    onRevokeAdvisorClick,
    reloadAdvisors,
    ep_id,
  } = props;
  const dispatch = useDispatch();

  const createAdvisorErrors = useSelector(getCreateAdvisorErrors);
  const advisorCreateLoading = useSelector(getCreateAdvisorLoading);
  const advisorUpdateLoading = useSelector(getUpdateAdvisorLoading);

  // Get enterprises list from store
  const enterprises = useSelector(allEnterprises);
  const enterpriseDetail = useParamSelector(getEnterpriseById, { id: ep_id?.toString() || '' });

  // Validation schema
  const validationSchema = yup.object().shape({
    first_name: yup.string().required('First name is required'),
    last_name: yup.string().required('Last name is required'),
    email: yup
      .string()
      .required('Email is required')
      .email('This must be a valid e-mail'),
    phone: yup.string().required('PhoneNo is required'),
    company_name: ep_id
      ? yup.string().nullable() // Optional when ep_id is provided
      : yup.string().required('CompanyName is required'), // Required when ep_id is null/undefined
  });

  const formRef: any = useRef();
  const initialValue: CreateOrEditAdvisorForm = {
    first_name: advisor?.firstName || '',
    last_name: advisor?.lastName || '',
    email: advisor?.email || '',
    phone: advisor?.phone || '',
    company_name: advisor?.companyName || (ep_id && enterpriseDetail ? enterpriseDetail.epName : ''),
    company_address: advisor?.companyAddress || '',
    trial: advisor?.trial || false,
    trial_duration: advisor?.trialDuration || '',
    ep_id: advisor?.epId || ep_id || undefined,
    is_ep_user: advisor?.epId ? true : !!ep_id,
  };

  // Fetch enterprises on component mount
  useEffect(() => {
    if (!ep_id) {
      dispatch(
        fetchEnterprisesList({
          filters: {},
        })
      );
    } else {
      dispatch(
        fetchEnterpriseById({
          id: ep_id,
          onSuccess: (data) => {
            console.log('Enterprise data received:', data);
          },
          onError: (error) => {
            console.error('Error fetching enterprise:', error);
          },
        })
      );
    }
  }, [dispatch]);

  // Prepare enterprise options for select
  const enterpriseOptions =
    enterprises?.map((enterprise) => ({
      label: enterprise.epName,
      value: enterprise.id.toString(),
    })) || [];

  // Handle form submission for create and edit
  const handleSubmit = (values: CreateOrEditAdvisorForm) => {
    // Prepare the payload with proper types
    const payload = { ...values };

    if (ep_id) {
      payload.ep_id = ep_id; // set enterpriseId by default for Advisor created from Enterprise admin
      if (enterpriseDetail) {
        payload.company_name = enterpriseDetail.epName;
      }
    }

    if (advisor) {
      dispatch(
        updateAdvisor({
          id: advisor.id.toString(),
          data: payload as AdvisorDTO,
          onSuccess: () => {
            handleModalVisibility(false);
            reloadAdvisors();
          },
        })
      );
    } else {
      dispatch(
        advisorCreate({
          advisorCreatePayload: payload as AdvisorDTO,
          onSuccess: () => {
            handleModalVisibility(false);
            reloadAdvisors();
          },
        })
      );
    }
  };

  // Handle validation errors from API
  useEffect(() => {
    if (createAdvisorErrors) {
      createAdvisorErrors.map((error: any) =>
        formRef.current.setFieldError(
          error.field,
          error.message.replace('_', ' ')
        )
      );
    }
  }, [createAdvisorErrors]);

  return (
    <div className='flex flex-col py-9 px-10'>
      <Formik
        innerRef={formRef}
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => {
          // Effect to handle enterprise selection and auto-fill company name
          useEffect(() => {
            // Handle enterprise context (when ep_id is provided as prop)
            if (ep_id && enterpriseDetail && enterpriseDetail.epName !== values.company_name) {
              setFieldValue('company_name', enterpriseDetail.epName);
            }
            // Handle enterprise selection from dropdown
            else if (values.is_ep_user && values.ep_id && !ep_id) {
              const selectedEnterprise = enterprises.find(
                (enterprise) => enterprise.id.toString() === values.ep_id?.toString()
              );
              if (selectedEnterprise && selectedEnterprise.epName !== values.company_name) {
                setFieldValue('company_name', selectedEnterprise.epName);
              }
            } else if (!values.is_ep_user && !ep_id) {
              // Clear company name when enterprise is deselected (only if not in enterprise context)
              setFieldValue('company_name', '');
            }
          }, [values.is_ep_user, values.ep_id, enterprises, setFieldValue, ep_id, enterpriseDetail]);

          return (
            <Form>
              <div className='grid grid-cols-2 gap-x-16 gap-y-1 max-w-[65.1875rem]'>
                <FormikInput
                  asterisk
                  name='first_name'
                  key='first_name'
                  label='First Name'
                  labelClassName='font-medium leading-6.5'
                />
                <FormikInput
                  asterisk
                  name='last_name'
                  key='last_name'
                  label='Last Name'
                  labelClassName='font-medium leading-6.5'
                />

                <FormikInput
                  asterisk
                  name='email'
                  key='email'
                  label='Email Address'
                  labelClassName='font-medium leading-6.5'
                  disabled={!!advisor}
                />

                <FormikInput
                  asterisk
                  name='phone'
                  key='phone'
                  label='Phone No'
                  labelClassName='font-medium leading-6.5'
                  disabled={!!advisor}
                />

                {!ep_id && (
                  <div className='col-span-2 flex py-5'>

                  <div className='flex items-center justify-between'>
                    <FormikCheckbox
                      name='trial'
                      key='trial'
                      text={<span>Free Trial</span>}
                      valueChanged={() => {
                        setFieldValue('trial', !values.trial);
                      }}
                    />

                    {values.trial && (
                      <FormikSelect
                        name='trial_duration'
                        key='trial_duration'
                        label='Trial Duration'
                        labelClassName='font-medium leading-6.5'
                        options={[
                          { label: '1 Month', value: '1' },
                          { label: '3 Months', value: '3' },
                          { label: '6 Months', value: '6' },
                          { label: '12 Months', value: '12' },
                        ]}
                        isSearch={true}
                        placeholder='Select Duration'
                        classname='w-60'
                      />
                    )}
                  </div>
                  <div className='flex items-center justify-between'>
                    <FormikCheckbox
                      name='is_ep_user'
                      key='is_ep_user'
                      text={<span>Is Enterprise User</span>}
                      valueChanged={() => {
                        setFieldValue('is_ep_user', !values.is_ep_user);
                      }}
                    />

                    {values.is_ep_user && (
                      <FormikSelect
                        name='ep_id'
                        key='ep_id'
                        label='Enterprise'
                        labelClassName='font-medium leading-6.5'
                        options={enterpriseOptions}
                        isSearch={true}
                        placeholder='Select Enterprise'
                        classname='w-60'
                      />
                    )}
                  </div>
                </div>
              )}
               <FormikInput
                  asterisk={!ep_id && !values.is_ep_user}
                  name='company_name'
                  key='company_name'
                  label='Company Name'
                  labelClassName='font-medium leading-6.5'
                  disabled={!!advisor || values.is_ep_user || !!ep_id}
                />

                <FormikInput
                  name='company_address'
                  key='company_address'
                  label='Company Address'
                  labelClassName='font-medium leading-6.5'
                  disabled={!!advisor}
                />
            </div>

            <div className='flex justify-between mt-[5rem]'>
              {advisor && onRevokeAdvisorClick ? (
                <Button
                  type='button'
                  className='px-6 py-2 bg-red-01 border-red-01'
                  onClick={onRevokeAdvisorClick}
                >
                  Delete
                </Button>
              ) : (
                <div />
              )}
              <div className='flex gap-5'>
                <Button
                  disabled={
                    advisor ? advisorUpdateLoading : advisorCreateLoading
                  }
                  isSubmitting={
                    advisor ? advisorUpdateLoading : advisorCreateLoading
                  }
                  className='px-6 py-2 w-[7.55rem]'
                  type='submit'
                >
                  {advisor ? 'Update' : 'Create'}
                </Button>
                <Button
                  className='px-6 py-2'
                  theme='secondary'
                  type='button'
                  onClick={() => {
                    handleModalVisibility(false);
                  }}
                  disabled={
                    advisor ? advisorUpdateLoading : advisorCreateLoading
                  }
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Form>
        );
        }}
      </Formik>
    </div>
  );
};

export default CreateOrEditAdvisorModal;
