import { Formik, Form } from 'formik';
import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import FormikFileInput from 'shared-resources/components/Input/FormikFileInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { Advisor } from 'models/entities/Advisor';
import { allOnlyRegAdvisors, getOnlyRegAdvisorsLoading } from 'store/selectors/advisor.selector';
import { fetchOnlyRegAdvisorsList } from 'store/actions/advisor.action';
import {
  createEnterpriseAdmin,
  updateEnterpriseAdmin,
} from 'store/actions/enterprise-admin.action';
import { enterpriseAdminLoading } from 'store/selectors/enterprise-admin.selector';
import FormikCheckbox from '../CheckBox/FormikCheckbox';

interface CreateOrEditEnterpriseAdminForm {
  ep_name: string;
  ep_logo?: File | null;
  location?: string;
  admin_type: 'existing' | 'new';
  existing_advisor_id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  trial?: boolean;
  trial_duration?: string;
}

interface CreateOrEditEnterpriseAdminModalProps {
  handleModalVisibility: (value: boolean) => void;
  enterpriseAdmin?: any; // Define proper type based on your enterprise admin entity
  onDeleteClick?: () => void;
  reloadEnterpriseAdmins: () => void;
}

const CreateOrEditEnterpriseAdminModal: React.FC<
  CreateOrEditEnterpriseAdminModalProps
> = (props) => {
  const {
    handleModalVisibility,
    enterpriseAdmin,
    onDeleteClick,
    reloadEnterpriseAdmins,
  } = props;
  const dispatch = useDispatch();

  // Get advisors list from store
  const advisors = useSelector(allOnlyRegAdvisors);
  const advisorsLoading = useSelector(getOnlyRegAdvisorsLoading);
  const loading = useSelector(enterpriseAdminLoading);

  // Fetch advisors on component mount
  useEffect(() => {
    dispatch(
      fetchOnlyRegAdvisorsList({
        filters: {},
      })
    );
  }, [dispatch]);

  // Validation schema
  const validationSchema = yup.object().shape({
    ep_name: yup.string().required('Enterprise name is required'),
    ep_logo: yup.mixed().nullable(),
    location: yup.string(),
    admin_type: yup.string().oneOf(['existing', 'new']).required(),
    existing_advisor_id: yup.string().when('admin_type', {
      is: 'existing',
      then: (schema) => schema.required('Please select an advisor'),
      otherwise: (schema) => schema.nullable(),
    }),
    first_name: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('First name is required'),
      otherwise: (schema) => schema.nullable(),
    }),
    last_name: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('Last name is required'),
      otherwise: (schema) => schema.nullable(),
    }),
    email: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) =>
        schema
          .required('Email is required')
          .email('This must be a valid e-mail'),
      otherwise: (schema) => schema.nullable(),
    }),
    phone: yup.string().when('admin_type', {
      is: 'new',
      then: (schema) => schema.required('Phone number is required'),
      otherwise: (schema) => schema.nullable(),
    }),
  });

  const formRef: any = useRef();
  const initialValue: CreateOrEditEnterpriseAdminForm = {
    ep_name: enterpriseAdmin?.enterpriseName || '',
    ep_logo: null,
    location: enterpriseAdmin?.location || '',
    admin_type: 'existing',
    existing_advisor_id: enterpriseAdmin?.advisorId || '',
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    trial: enterpriseAdmin?.trial || false,
    trial_duration: enterpriseAdmin?.trialDuration || '',
  };

  // Handle form submission for create and edit
  const handleSubmit = async (values: CreateOrEditEnterpriseAdminForm) => {
    const enterpriseAdminData = {
      ep_name: values.ep_name,
      ep_logo: values.ep_logo || undefined,
      location: values.location,
      is_existing_advisor: values.admin_type === 'existing',
      existing_advisor_id: values.existing_advisor_id,
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone: values.phone,
      trial: values.trial,
      trial_duration: values.trial_duration,
    };

    const onSuccess = () => {
      handleModalVisibility(false);
      reloadEnterpriseAdmins();
    };

    const onError = (error: string) => {
      console.error('Error creating/updating enterprise admin:', error);
    };

    if (enterpriseAdmin) {
      // Update existing enterprise admin
      dispatch(
        updateEnterpriseAdmin({
          id: enterpriseAdmin.id,
          data: enterpriseAdminData,
          onSuccess,
          onError,
        })
      );
    } else {
      // Create new enterprise admin
      dispatch(
        createEnterpriseAdmin({
          enterpriseAdminCreatePayload: enterpriseAdminData,
          onSuccess,
          onError,
        })
      );
    }
  };

  // Prepare advisor options for select
  const advisorOptions =
    advisors?.map((advisor: Advisor) => ({
      label: `${advisor.firstName} ${advisor.lastName} (${advisor.email})`,
      value: advisor.id.toString(),
    })) || [];

  return (
    <div className='flex flex-col py-9 px-10'>
      <Formik
        innerRef={formRef}
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className='space-y-6'>
              {/* Enterprise Information Section */}
              <div>
                <h3 className='text-lg font-semibold mb-4'>
                  Enterprise Information
                </h3>
                <div className='grid grid-cols-2 gap-x-16 gap-y-4'>
                  <FormikInput
                    asterisk
                    name='ep_name'
                    key='ep_name'
                    label='Enterprise Name'
                    labelClassName='font-medium leading-6.5'
                  />

                  <FormikInput
                    name='location'
                    key='location'
                    label='Location'
                    labelClassName='font-medium leading-6.5'
                  />
                </div>

                <div className='mt-4'>
                  <FormikFileInput
                    name='ep_logo'
                    label='Enterprise Logo'
                    accept='image/*'
                    placeholder='Click to upload logo or drag and drop'
                    helperText='Accepted formats: JPG, PNG, GIF'
                  />
                </div>
              </div>

              {/* Enterprise Admin Details Section */}
              <div>
                <h3 className='text-lg font-semibold mb-4'>
                  Enterprise Admin Details
                </h3>

                {/* Admin Type Selection */}
                <div className='mb-4'>
                  <h4 className='text-xl font-medium mb-3 leading-6.5'>
                    Admin Type <span className='text-red-500 ml-1'>*</span>
                  </h4>
                  <FormikRadio
                    name='admin_type'
                    options={[
                      {
                        label: 'Choose From Existing Advisor',
                        value: 'existing',
                      },
                      { label: 'Create New Admin', value: 'new' },
                    ]}
                    className='flex gap-8'
                    valueChanged={(selectedValue) => {
                      // Clear fields when switching types
                      if (selectedValue === 'existing') {
                        setFieldValue('first_name', '');
                        setFieldValue('last_name', '');
                        setFieldValue('email', '');
                        setFieldValue('phone', '');
                      } else if (selectedValue === 'new') {
                        setFieldValue('existing_advisor_id', '');
                      }
                    }}
                  />
                </div>

                {/* Existing Advisor Selection */}
                {values.admin_type === 'existing' && (
                  <div className='grid grid-cols-1 gap-4'>
                    <FormikSelect
                      asterisk
                      name='existing_advisor_id'
                      label='Select Advisor'
                      labelClassName='font-medium leading-6.5'
                      options={advisorOptions}
                      placeholder='Choose an advisor'
                      classname='w-full'
                      isSearch={true}
                    />
                  </div>
                )}

                {/* New Admin Creation Fields */}
                {values.admin_type === 'new' && (
                  <div className='grid grid-cols-2 gap-x-16 gap-y-4'>
                    <FormikInput
                      asterisk
                      name='first_name'
                      key='first_name'
                      label='First Name'
                      labelClassName='font-medium leading-6.5'
                    />
                    <FormikInput
                      asterisk
                      name='last_name'
                      key='last_name'
                      label='Last Name'
                      labelClassName='font-medium leading-6.5'
                    />
                    <FormikInput
                      asterisk
                      name='email'
                      key='email'
                      label='Email Address'
                      labelClassName='font-medium leading-6.5'
                      type='email'
                    />
                    <FormikInput
                      asterisk
                      name='phone'
                      key='phone'
                      label='Phone Number'
                      labelClassName='font-medium leading-6.5'
                      type='number'
                    />

                    <div className='flex items-center justify-between'>
                      <FormikCheckbox
                        name='trial'
                        key='trial'
                        text={<span>Free Trial</span>}
                        valueChanged={() => {
                          setFieldValue('trial', !values.trial);
                        }}
                      />

                      {values.trial && (
                        <FormikSelect
                          name='trial_duration'
                          key='trial_duration'
                          label='Trial Duration'
                          labelClassName='font-medium leading-6.5'
                          options={[
                            { label: '1 Month', value: '1' },
                            { label: '3 Months', value: '3' },
                            { label: '6 Months', value: '6' },
                            { label: '12 Months', value: '12' },
                          ]}
                          isSearch={true}
                          placeholder='Select Duration'
                          classname='w-60'
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className='flex justify-between mt-[5rem]'>
              {enterpriseAdmin && onDeleteClick ? (
                <Button
                  type='button'
                  className='px-6 py-2 bg-red-01 border-red-01'
                  onClick={onDeleteClick}
                >
                  Delete
                </Button>
              ) : (
                <div />
              )}
              <div className='flex gap-5'>
                <Button
                  className='px-6 py-2 w-[7.55rem]'
                  type='submit'
                  disabled={loading}
                >
                  {loading
                    ? 'Processing...'
                    : enterpriseAdmin
                    ? 'Update'
                    : 'Create'}
                </Button>
                <Button
                  className='px-6 py-2'
                  theme='secondary'
                  type='button'
                  onClick={() => {
                    handleModalVisibility(false);
                  }}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateOrEditEnterpriseAdminModal;
