@import 'tailwindcss/base';
@import './shared-resources/styles/components.styles.css';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

body {
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;

  #root {
    height: 100vh;
  }
}

@layer utilities {
  .scrollbar::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .scrollbar::-webkit-scrollbar-track {
    @apply rounded-full bg-blue-02;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    @apply rounded-full bg-blue-300;
  }

  .scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-blue-01;
  }

  .owner-card:hover .inner-card {
    @apply border-blue-02;
  }
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-always-visible {
    overflow-y: scroll !important;
    scrollbar-width: thin !important; /* Firefox */
    scrollbar-color: #93C5FD #E1F0F6 !important; /* Firefox */
    scrollbar-gutter: stable !important; /* Reserve space for scrollbar */
  }

  .scrollbar-always-visible::-webkit-scrollbar {
    width: 12px !important;
    background: transparent !important;
    display: block !important;
  }

  .scrollbar-always-visible::-webkit-scrollbar-track {
    background: #E1F0F6 !important;
    border-radius: 6px !important;
    margin: 2px !important;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb {
    background: #93C5FD !important;
    border-radius: 6px !important;
    border: 2px solid #E1F0F6 !important;
    min-height: 20px !important;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
    background: #20A0D6 !important;
  }

  .scrollbar-always-visible::-webkit-scrollbar-button {
    display: none !important;
  }
}

.ql-toolbar button {
  pointer-events: auto !important;
}

.ql-toolbar .ql-formats {
  z-index: 1;
  position: relative;
}

// New Quill editor styles
.quill-wrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 17rem);
  
}

.ql-toolbar.ql-snow {
  border: 1px solid #20A0D6 !important;
  border-bottom: none !important;
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 9px !important;
  padding: 8px !important;
  
}

.ql-container.ql-snow {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #20A0D6 !important;
  border-top: none !important;
  border-bottom-left-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
  overflow: auto;

}

.ql-editor-pdf {
  height: 100%;

  h1 {
    font-size: 2rem !important;
  }
  
  h2 {
    font-size: 1.5rem !important;
  }
  
  h3 {
    font-size: 1.17rem !important;
  }
  
  h4 {
    font-size: 1rem !important;
  }
  
  h5 {
    font-size: 0.83rem !important;
  }

  h6 {
    font-size: 0.67rem !important;
  }
}

.ql-toolbar.ql-snow button {
  width: 24px !important;
  height: 24px !important;
}
.ql-toolbar.ql-snow .ql-formats {
  display: flex !important;
  gap: 24px !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}


.tool-section-header {
  font-size: 1.5em;
  font-weight: bold;
  margin: 1em 0;
  padding: 0.5em;
  background-color: #f5f5f5;
  border-left: 4px solid #007bff;  /* Blue accent for visual hierarchy */
}

.sign-in-header {
  box-shadow:  4px 6px -6px #222
}