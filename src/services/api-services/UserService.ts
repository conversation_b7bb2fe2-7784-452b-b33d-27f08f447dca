import { UserProfileUpdateDTO } from 'dtos/business-owner.dto';
import { UserType } from 'types/enum';
import { baseApiService } from './BaseApiService';

class UserService {
  static getInstance(): UserService {
    return new UserService();
  }

  public async updateBusinessOwnerUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/business-owner/me/update', data);
  }

  public async updateAdvisorUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/advisor/me/update', data);
  }


  public async updateEnterpriseAdminUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/enterprise/me/update', data);
  }


  public async updateSuperAdminUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/admin/me/update', data);
  }

  public async updateAdvisorUserProfileByAdmin(
    data: UserProfileUpdateDTO,
    user_id: string
  ) {
    return baseApiService.post('/advisor/update', data, {
      params: { user_id },
    });
  }

  public async setupMFA(data: {
    email: string;
    mfa_preference: 'email' | 'totp';
    user_type: UserType;
  }) {
    return baseApiService.post('/mfa', data);
  }

  public async verifyMFASetup(data: {
    email: string;
    verification_code: string;
    user_type: UserType;
    secret: string;
  }) {
    return baseApiService.post('/mfa/verify-setup', data);
  }

  public async verifyMFATOTP(data: {
    email: string;
    verification_code: string;
    user_type: UserType;
    secret: string;
    is_backup?: boolean;
  }) {
    return baseApiService.post('/mfa/verify-totp', data);
  }
}

export const userService = UserService.getInstance();
