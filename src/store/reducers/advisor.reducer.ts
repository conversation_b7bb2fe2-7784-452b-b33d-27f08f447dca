import {
  createEntityAdapter,
  createSlice,
  EntityId,
  EntityState,
  PayloadAction,
} from '@reduxjs/toolkit';
import { uniq } from 'lodash';
import { Advisor } from 'models/entities/Advisor';
import { BaseEntityStore } from 'store/types/BaseEntityStoreType';
import { PaginationMeta } from '../../types/pagination.types';

const advisorAdapter = createEntityAdapter<Advisor>();

const initialState: EntityState<Advisor> &
  BaseEntityStore &
  PaginationMeta & {
    onlyRegAdvisors: {
      ids: EntityId[];
      entities: Record<EntityId, Advisor>;
      loading: boolean;
      error: boolean;
      message?: string;
    };
  } = {
  ...advisorAdapter.getInitialState(),
  create: {},
  update: {},
  get: {},
  list: {},
  paginationMeta: {},
  onlyRegAdvisors: {
    ids: [],
    entities: {},
    loading: false,
    error: false,
    message: undefined,
  },
};

export const advisorSlice = createSlice({
  name: 'advisor',
  initialState,
  reducers: {
    fetch_advisor_list: advisorAdapter.addMany,
    add_one: advisorAdapter.setOne,
    set_advisors: (
      state,
      action: PayloadAction<{
        ids: EntityId[];
        loadMore: boolean;
      }>
    ) => {
      const { ids, loadMore } = action.payload;
      state.ids = loadMore ? uniq([...state.ids, ...ids]) : ids;
    },
    set_created_advisor: (
      state,
      action: PayloadAction<{ owner: Advisor }>
    ) => {
      const { owner } = action.payload;
      const entity = { [owner.id]: owner };
      state.entities = { ...state.entities, ...entity };
      state.ids = [owner.id, ...state.ids];
    },
    set_update_advisor: (state, action: any) => {
      const payload = action?.payload;
      const advisor = payload?.advisor;

      if (payload && advisor) {
        const { id } = advisor;
        const entity = { [id]: advisor };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.map((id) =>
          id === advisor.id ? advisor.id : id
        );
      }
    },
    set_delete_advisor: (state, action: any) => {
      const payload = action?.payload;
      const advisor = payload?.advisor;
      if (payload && advisor) {
        const { id } = advisor;
        const entity = { [id]: advisor };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.filter((i) => i !== id);
      }
    },

    set_create_advisor_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.create.loading = loading;
    },
    set_update_advisor_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.update.loading = loading;
    },
    set_create_advisor_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.create.error = error;
      state.create.errors = errors;
      state.create.message = message;
      state.create.loading = false;
    },
    set_update_advisor_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.update.error = error;
      state.update.errors = errors;
      state.update.message = message;
      state.update.loading = false;
    },
    set_get_advisor_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.get = { ...state.get, loading };
    },
    set_get_advisor_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.get = { ...state.get, loading: false, error, message };
    },
    set_advisors_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.list = { ...state.list, loading };
    },
    set_advisors_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.list = { ...state.list, error, message };
    },
   
    set_page_data: (state, action: PayloadAction<PaginationMeta>) => {
      state.paginationMeta = action.payload.paginationMeta;
    },

    // Only Registered Advisors reducers
    set_only_reg_advisors_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.onlyRegAdvisors.loading = loading;
    },
    set_only_reg_advisors_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.onlyRegAdvisors.error = error;
      state.onlyRegAdvisors.message = message;
      state.onlyRegAdvisors.loading = false;
    },
    set_only_reg_advisors: (
      state,
      action: PayloadAction<{ advisors: Advisor[] }>
    ) => {
      const { advisors } = action.payload;
      const entities: Record<EntityId, Advisor> = {};
      const ids: EntityId[] = [];

      advisors.forEach((advisor) => {
        entities[advisor.id] = advisor;
        ids.push(advisor.id);
      });

      state.onlyRegAdvisors.entities = entities;
      state.onlyRegAdvisors.ids = ids;
      state.onlyRegAdvisors.loading = false;
      state.onlyRegAdvisors.error = false;
    },

  },
});

export const {
  add_one: addAdvisor,
  fetch_advisor_list: getAdvisors,
  set_created_advisor: setCreatedAdvisor,
  set_create_advisor_loading: setCreateAdvisorLoading,
  set_create_advisor_error: setCreateAdvisorError,
  set_update_advisor_loading: setUpdateAdvisorLoading,
  set_update_advisor_error: setUpdateAdvisorError,
  set_get_advisor_loading: setGetAdvisorLoading,
  set_get_advisor_error: setGetAdvisorError,

  set_advisors_loading: setAdvisorsLoading,
  set_advisors_error: setAdvisorsError,
  set_page_data: setAdvisorsPageData,
  set_advisors: setAdvisors,

  set_update_advisor: setUpdateAdvisor,
  set_delete_advisor: setDeleteAdvisor,

  // Only Registered Advisors actions
  set_only_reg_advisors_loading: setOnlyRegAdvisorsLoading,
  set_only_reg_advisors_error: setOnlyRegAdvisorsError,
  set_only_reg_advisors: setOnlyRegAdvisors,
} = advisorSlice.actions;

export default advisorSlice.reducer;
