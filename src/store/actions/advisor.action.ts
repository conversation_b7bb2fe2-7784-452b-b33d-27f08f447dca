import { AdvisorDTO } from "dtos/advisor.dto";
import { AdvisorActionType, BusinessOwnerActionType } from "./actions.constants";
import { AdvisorsFilters } from "types/Advisor.type";

export interface AdvisorCreateActionPayloadType {
  advisorCreatePayload: AdvisorDTO;
  onSuccess: () => void;
}

export const advisorCreate = (
  payload: AdvisorCreateActionPayloadType
) => ({
  type: AdvisorActionType.CREATE_ADVISOR,
  payload,
});

export const updateAdvisor = (payload: {
  id: string;
  data: AdvisorDTO;
  onSuccess: () => void;
}) => ({
  type: AdvisorActionType.UPDATE_ADVISOR,
  payload,
});

export const fetchAdvisorsList = (payload: {
  filters: AdvisorsFilters;
}) => ({
  type: AdvisorActionType.GET_ADVISORS,
  payload,
});


export const updateAdvisorAccess = (payload: {
  id: number;
  isActive: string;
  onSuccess: () => void;
}) => ({
  type: AdvisorActionType.ACCESS_ADVISOR,
  payload,
});

export const fetchOnlyRegAdvisorsList = (payload: {
  filters: AdvisorsFilters;
}) => ({
  type: AdvisorActionType.GET_ONLY_REG_ADVISORS,
  payload,
});